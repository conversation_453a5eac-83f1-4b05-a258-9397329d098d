/// API constants for the NextSportz application

class ApiConst {
  static String baseUrl = "https://apidev.nextsportz.com";

  // Authentication endpoints
  static String registerEndpoint = "/auth/register";
  static String verifyOtpEndpoint = "/auth/verify-otp";
  static String loginEndpoint = "/auth/login";
  static String meEndpoint = "/auth/me";
  static String refreshTokenEndpoint = "/auth/refresh";
  static String logoutEndpoint = "/auth/logout";
  static String forgotPasswordEndpoint = "/auth/forgot-password";
  static String resetPasswordEndpoint = "/auth/reset-password";
  static String updateProfileEndpoint = "/auth/update-profile";

  // Player endpoints
  static String playersEndpoint = "/api/players";
  static String playerProfileEndpoint = "/api/players/{playerId}";
  static String playerRatingEndpoint = "/api/players/{playerId}/rating";

  // Challenge endpoints
  static String challengesEndpoint = "/api/challenges";
  static String challengeDetailEndpoint = "/api/challenges/{challengeId}";
  static String respondToChallengeEndpoint = "/api/challenges/respond";
  static String submitMatchResultEndpoint = "/api/challenges/result";
  static String disputeMatchResultEndpoint = "/api/challenges/dispute";
  static String challengeStatsEndpoint = "/api/challenges/stats";
  static String challengeSuggestionsEndpoint = "/api/challenges/suggestions";
  static String myChallengesEndpoint = "/api/challenges/my-challenges";
  static String matchRequestsEndpoint = "/api/challenges/match-requests";
  static String myMatchRequestsEndpoint = "/api/challenges/my-match-requests";

  // Teams endpoints
  static String teamsEndpoint = "/api/teams";
  static String teamDetailEndpoint = "/api/teams/{teamId}";
  static String myTeamsEndpoint = "/api/teams/my-teams";
  static String teamInvitationsEndpoint = "/api/teams/invitations";
  static String teamMembersEndpoint = "/api/teams/{teamId}/members";
  static String teamMemberRoleEndpoint =
      "/api/teams/{teamId}/members/{memberId}/role";
  static String teamInvitePlayerEndpoint = "/api/teams/{teamId}/invite";
  static String teamInvitationResponseEndpoint =
      "/api/teams/invitations/{invitationId}/respond";

  // File upload endpoints
  static String fileUploadEndpoint = "/api/upload";
}
