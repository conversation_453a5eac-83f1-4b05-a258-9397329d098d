import 'package:fpdart/fpdart.dart';
import '../../domain/entities/user.dart';
import '../../domain/repositories/auth_repository.dart';
import '../dto/auth_request_models.dart';

import '../datasources/auth_datasource.dart';
import '../../../../core/local/token_storage.dart';
import '../../../../core/networking/app_error.dart';
import '../../../../core/networking/exception.dart';

class AuthRepositoryImpl implements AuthRepository {
  final AuthDatasource _authDatasource;
  final TokenStorageService _tokenStorage;

  AuthRepositoryImpl(this._authDatasource, this._tokenStorage);
  @override
  Future<Either<AppError, User>> loginWithPhone({
    required String phoneNumber,
    required String password,
    required String role,
  }) async {
    try {
      final request = LoginRequest(
        phoneNumber: phoneNumber,
        password: password,
        role: role,
      );

      final response = await _authDatasource.login(request);

      // Check if login was successful (tokens exist)
      if (response.tokens?.accessToken == null ||
          response.tokens?.refreshToken == null) {
        return const Left(AppError('Login failed'));
      }

      // Save tokens
      await _tokenStorage.saveTokens(
        accessToken: response.tokens!.accessToken!,
        refreshToken: response.tokens!.refreshToken!,
      );

      // After saving tokens, fetch user data from /me endpoint
      final userResponse = await _authDatasource.getCurrentUser();

      // Create user from /me response
      final user = User(
        id: userResponse.userId!,
        name: userResponse.fullName!,
        email: userResponse.email!,
        phoneNumber: userResponse.phoneNumber ?? phoneNumber,
        role: userResponse.role ?? role,
        playerId: userResponse.playerIds?.first.id,
        createdAt: userResponse.createdAt ?? DateTime.now(),
        updatedAt: userResponse.updatedAt ?? DateTime.now(),
        isActive: true,
      );

      return Right(user);
    } on DioExceptionHandle catch (e) {
      return Left(AppError(e.message));
    } catch (e) {
      return Left(AppError(e.toString()));
    }
  }

  @override
  Future<Either<AppError, void>> verifyOtp({
    required String phoneNumber,
    required String otp,
  }) async {
    try {
      final request = RegisterVerifyRequest(
        phoneNumber: phoneNumber,
        otp: otp,
      );

      final response = await _authDatasource.verifyOtp(request);

      // Check if OTP verification was successful
      if (response.success != true) {
        return Left(AppError(response.message ?? 'OTP verification failed'));
      }

      return const Right(null);
    } on DioExceptionHandle catch (e) {
      return Left(AppError(e.message));
    } catch (e) {
      return Left(AppError(e.toString()));
    }
  }

  @override
  Future<Either<AppError, void>> register({
    required String name,
    required String email,
    required String phoneNumber,
    required String password,
    required String role,
  }) async {
    try {
      final request = RegisterRequest(
        fullName: name,
        email: email,
        phoneNumber: phoneNumber,
        password: password,
        role: role,
      );

      final response = await _authDatasource.register(request);

      // Check if registration was successful
      if (response.success != true) {
        return Left(AppError(response.message ?? 'Registration failed'));
      }

      return const Right(null);
    } on DioExceptionHandle catch (e) {
      return Left(AppError(e.message));
    } catch (e) {
      return Left(AppError(e.toString()));
    }
  }

  @override
  Future<Either<AppError, void>> forgotPassword({required String email}) async {
    try {
      final request = ForgotPasswordRequest(email: email);
      await _authDatasource.forgotPassword(request);
      return const Right(null);
    } on DioExceptionHandle catch (e) {
      return Left(AppError(e.message));
    } catch (e) {
      return Left(AppError(e.toString()));
    }
  }

  @override
  Future<Either<AppError, void>> resetPassword({
    required String token,
    required String newPassword,
    required String confirmPassword,
  }) async {
    try {
      if (newPassword != confirmPassword) {
        return const Left(AppError('Passwords do not match'));
      }

      if (newPassword.length < 6) {
        return const Left(AppError('Password must be at least 6 characters'));
      }

      final request = ResetPasswordRequest(
        token: token,
        newPassword: newPassword,
        confirmPassword: confirmPassword,
      );

      await _authDatasource.resetPassword(request);
      return const Right(null);
    } on DioExceptionHandle catch (e) {
      return Left(AppError(e.message));
    } catch (e) {
      return Left(AppError(e.toString()));
    }
  }

  @override
  Future<Either<AppError, void>> logout() async {
    try {
      // Get current refresh token to pass to the logout endpoint
      final tokens = await _tokenStorage.getTokens();
      final refreshToken = tokens?['refreshToken'];

      final response = await _authDatasource.logout(refreshToken: refreshToken);

      // Check if logout was successful
      if (response.success != true) {
        await _tokenStorage.clearTokens();
        return Left(AppError(response.message ?? 'Logout failed'));
      }

      // Clear tokens on successful logout
      await _tokenStorage.clearTokens();

      return const Right(null);
    } on DioExceptionHandle catch (e) {
      // Clear tokens even if logout request fails
      await _tokenStorage.clearTokens();
      return Left(AppError(e.message));
    } catch (e) {
      // Clear tokens even if logout request fails
      await _tokenStorage.clearTokens();
      return Left(AppError(e.toString()));
    }
  }

  @override
  Future<Either<AppError, User?>> getCurrentUser() async {
    try {
      final response = await _authDatasource.getCurrentUser();

      if (response.userId == null) {
        return const Right(null);
      }

      final user = User(
        id: response.userId ?? '1',
        name: response.fullName ?? 'User',
        email: response.email ?? '',
        phoneNumber: response.phoneNumber ?? '',
        role: response.role ?? 'PLAYER',
        playerId: response.playerIds?.first.id,
        createdAt: response.createdAt ?? DateTime.now(),
        updatedAt: response.updatedAt ?? DateTime.now(),
        isActive: true,
      );

      return Right(user);
    } on DioExceptionHandle catch (e) {
      return Left(AppError(e.message));
    } catch (e) {
      return Left(AppError(e.toString()));
    }
  }

  @override
  Future<Either<AppError, void>> updateProfile({
    required String name,
    required String email,
    String? profileImage,
  }) async {
    try {
      final request = UpdateProfileRequest(
        fullName: name,
        email: email,
        profileImage: profileImage,
      );

      await _authDatasource.updateProfile(request);
      return const Right(null);
    } on DioExceptionHandle catch (e) {
      return Left(AppError(e.message));
    } catch (e) {
      return Left(AppError(e.toString()));
    }
  }

  @override
  Future<Either<AppError, bool>> isAuthenticated() async {
    try {
      // First check if we have tokens
      final tokens = await _tokenStorage.getTokens();

      if (tokens == null) {
        return const Right(false);
      }

      // If we have tokens, try to get current user
      final user = await getCurrentUser();
      return user.fold(
        (error) {
          return Left(error);
        },
        (user) {
          return Right(user != null);
        },
      );
    } catch (e) {
      return Left(AppError(e.toString()));
    }
  }
}
