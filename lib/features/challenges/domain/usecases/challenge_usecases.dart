import 'package:fpdart/fpdart.dart';
import '../../../../core/networking/app_error.dart';
import '../../../../core/use_case.dart';
import '../entities/challenge.dart';
import '../entities/challenge_enums.dart';
import '../repositories/challenge_repository.dart';
import '../../data/dto/challenge_dto.dart';

// Challenge Management Use Cases

class GetChallengesUseCase
    implements UseCase<PagedChallenges, GetChallengesParams> {
  final ChallengeRepository repository;

  GetChallengesUseCase(this.repository);

  @override
  Future<Either<AppError, PagedChallenges>> call(GetChallengesParams params) {
    return repository.getChallenges(
      matchType: params.matchType,
      location: params.location,
      skillLevel: null, // Add if needed
      ageGroup: null, // Add if needed
      maxWagerAmount: params.maxWager,
      hasWager: params.minWager != null,
      fromDate: null, // Add if needed
      toDate: null, // Add if needed
      page: null, // Add if needed
      pageSize: params.limit,
    );
  }
}

class GetChallengeByIdUseCase implements UseCase<Challenge, String> {
  final ChallengeRepository repository;

  GetChallengeByIdUseCase(this.repository);

  @override
  Future<Either<AppError, Challenge>> call(String challengeId) {
    return repository.getChallengeById(challengeId);
  }
}

class CreateChallengeUseCase
    implements UseCase<Challenge, CreateChallengeParams> {
  final ChallengeRepository repository;

  CreateChallengeUseCase(this.repository);

  @override
  Future<Either<AppError, Challenge>> call(CreateChallengeParams params) {
    final request = CreateChallengeRequestDto(
      matchType: params.matchType,
      ageGroup: params.ageGroup?.toString(),
      skillLevel: params.skillLevel,
      location: params.location,
      venueId: params.preferredVenue,
      proposedDateTime: params.preferredDateTime,
      alternativeDateTime1: params.alternativeDateTime1,
      alternativeDateTime2: params.alternativeDateTime2,
      wagerAmount: params.wagerAmount,
      wagerType: params.wagerType?.toString(),
      description: params.description,
      rules: params.rules,
      specificOpponentId: params.specificOpponentId,
      specificOpponentTeamId: params.specificOpponentTeamId,
      challengerTeamId: params.challengerTeamId,
      expirationHours: params.expirationHours,
    );

    return repository.createChallenge(request);
  }
}

class UpdateChallengeUseCase
    implements UseCase<Challenge, UpdateChallengeParams> {
  final ChallengeRepository repository;

  UpdateChallengeUseCase(this.repository);

  @override
  Future<Either<AppError, Challenge>> call(UpdateChallengeParams params) {
    final request = UpdateChallengeRequestDto(
      matchType: params.matchType,
      location: params.location,
      venueId: null, // Add if needed
      proposedDateTime: params.preferredDateTime,
      alternativeDateTime1: null, // Add if needed
      alternativeDateTime2: null, // Add if needed
      wagerAmount: params.wagerAmount,
      wagerType: params.wagerType?.toString(),
      description: params.description,
      rules: null, // Add if needed
    );

    return repository.updateChallenge(params.challengeId, request);
  }
}

class DeleteChallengeUseCase implements UseCase<void, String> {
  final ChallengeRepository repository;

  DeleteChallengeUseCase(this.repository);

  @override
  Future<Either<AppError, void>> call(String challengeId) {
    return repository.deleteChallenge(challengeId);
  }
}

class RespondToChallengeUseCase
    implements UseCase<Challenge, RespondToChallengeParams> {
  final ChallengeRepository repository;

  RespondToChallengeUseCase(this.repository);

  @override
  Future<Either<AppError, Challenge>> call(RespondToChallengeParams params) {
    final request = RespondToChallengeRequestDto(
      challengeId: params.challengeId,
      responseType: params.response,
      message: params.message,
    );

    return repository.respondToChallenge(request);
  }
}

class SubmitMatchResultUseCase
    implements UseCase<void, SubmitMatchResultParams> {
  final ChallengeRepository repository;

  SubmitMatchResultUseCase(this.repository);

  @override
  Future<Either<AppError, void>> call(SubmitMatchResultParams params) {
    final request = SubmitMatchResultRequestDto(
      challengeId: params.challengeId,
      winnerId: params.winnerId,
      matchResult: '${params.winnerScore}-${params.loserScore}',
      comments: params.notes,
    );

    return repository.submitMatchResult(request);
  }
}

class DisputeMatchResultUseCase
    implements UseCase<void, DisputeMatchResultParams> {
  final ChallengeRepository repository;

  DisputeMatchResultUseCase(this.repository);

  @override
  Future<Either<AppError, void>> call(DisputeMatchResultParams params) {
    final request = DisputeMatchResultRequestDto(
      challengeId: params.challengeId,
      disputeReason: params.reason,
      evidence: params.evidence,
    );

    return repository.disputeMatchResult(request);
  }
}

class GetChallengeStatsUseCase implements UseCase<ChallengeStats, NoParams> {
  final ChallengeRepository repository;

  GetChallengeStatsUseCase(this.repository);

  @override
  Future<Either<AppError, ChallengeStats>> call(NoParams params) {
    return repository.getChallengeStats();
  }
}

class GetMatchSuggestionsUseCase
    implements UseCase<List<MatchSuggestion>, NoParams> {
  final ChallengeRepository repository;

  GetMatchSuggestionsUseCase(this.repository);

  @override
  Future<Either<AppError, List<MatchSuggestion>>> call(NoParams params) {
    return repository.getMatchSuggestions();
  }
}

class GetMyChallengesUseCase
    implements UseCase<PagedChallenges, GetMyChallengesParams> {
  final ChallengeRepository repository;

  GetMyChallengesUseCase(this.repository);

  @override
  Future<Either<AppError, PagedChallenges>> call(GetMyChallengesParams params) {
    return repository.getMyChallenges(
      page: params.page,
      pageSize: params.pageSize,
    );
  }
}

class GetMatchRequestsUseCase
    implements UseCase<List<MatchSuggestion>, GetMatchRequestsParams> {
  final ChallengeRepository repository;

  GetMatchRequestsUseCase(this.repository);

  @override
  Future<Either<AppError, List<MatchSuggestion>>> call(
      GetMatchRequestsParams params) {
    return repository.getMatchRequests(
      matchType: params.matchType,
      location: params.location,
      skillLevel: params.skillLevel,
      ageGroup: params.ageGroup?.toString(),
      maxDistance: params.maxDistance,
      acceptWagers: params.acceptWagers,
      page: params.page,
      pageSize: params.pageSize,
    );
  }
}

class CreateMatchRequestUseCase
    implements UseCase<MatchSuggestion, CreateMatchRequestParams> {
  final ChallengeRepository repository;

  CreateMatchRequestUseCase(this.repository);

  @override
  Future<Either<AppError, MatchSuggestion>> call(
      CreateMatchRequestParams params) {
    final request = CreateMatchRequestRequestDto(
      matchType: params.matchType,
      preferredLocation: params.preferredLocation,
      skillLevel: params.skillLevel,
      ageGroup: params.ageGroup?.toString(),
      preferredDateTime: params.preferredDateTime,
      description: params.description,
      maxDistance: params.maxDistance,
      maxWagerAmount: params.maxWagerAmount,
      acceptWagers: params.acceptWagers,
      expirationHours: params.expirationHours,
    );

    return repository.createMatchRequest(request);
  }
}

class GetMyMatchRequestsUseCase
    implements UseCase<List<MatchSuggestion>, GetMyMatchRequestsParams> {
  final ChallengeRepository repository;

  GetMyMatchRequestsUseCase(this.repository);

  @override
  Future<Either<AppError, List<MatchSuggestion>>> call(
      GetMyMatchRequestsParams params) {
    return repository.getMyMatchRequests(
      page: params.page,
      pageSize: params.pageSize,
    );
  }
}

// Parameter classes

class GetChallengesParams {
  final double? lat;
  final double? lng;
  final String? matchType;
  final String? location;
  final double? minWager;
  final double? maxWager;
  final String? status;
  final int? limit;
  final int? offset;

  GetChallengesParams({
    this.lat,
    this.lng,
    this.matchType,
    this.location,
    this.minWager,
    this.maxWager,
    this.status,
    this.limit,
    this.offset,
  });
}

class CreateChallengeParams {
  final String matchType;
  final AgeGroup? ageGroup;
  final String? skillLevel;
  final String location;
  final String? preferredVenue; // Legacy field for backward compatibility
  final List<String> selectedVenueIds; // New field for multiple venues
  final DateTime preferredDateTime;
  final DateTime? alternativeDateTime1;
  final DateTime? alternativeDateTime2;
  final String? timeSlot;
  final double wagerAmount;
  final WagerType? wagerType;
  final String? description;
  final String? rules;
  final String? specificOpponentId;
  final String? specificOpponentTeamId;
  final String? challengerTeamId;
  final List<String> tags;
  final int expirationHours;

  CreateChallengeParams({
    required this.matchType,
    this.ageGroup,
    this.skillLevel,
    required this.location,
    this.preferredVenue,
    this.selectedVenueIds = const [],
    required this.preferredDateTime,
    this.alternativeDateTime1,
    this.alternativeDateTime2,
    this.timeSlot,
    required this.wagerAmount,
    this.wagerType,
    this.description,
    this.rules,
    this.specificOpponentId,
    this.specificOpponentTeamId,
    this.challengerTeamId,
    required this.tags,
    this.expirationHours = 168, // 7 days default
  });
}

class UpdateChallengeParams {
  final String challengeId;
  final String? matchType;
  final String? location;
  final String? preferredVenue;
  final DateTime? preferredDateTime;
  final String? timeSlot;
  final double? wagerAmount;
  final WagerType? wagerType;
  final String? description;
  final List<String>? tags;
  final DateTime? expiresAt;

  UpdateChallengeParams({
    required this.challengeId,
    this.matchType,
    this.location,
    this.preferredVenue,
    this.preferredDateTime,
    this.timeSlot,
    this.wagerAmount,
    this.wagerType,
    this.description,
    this.tags,
    this.expiresAt,
  });
}

class RespondToChallengeParams {
  final String challengeId;
  final String response; // 'accept', 'decline', 'counter'
  final String? message;

  RespondToChallengeParams({
    required this.challengeId,
    required this.response,
    this.message,
  });
}

class SubmitMatchResultParams {
  final String challengeId;
  final String winnerId;
  final String loserId;
  final int winnerScore;
  final int loserScore;
  final String? notes;

  SubmitMatchResultParams({
    required this.challengeId,
    required this.winnerId,
    required this.loserId,
    required this.winnerScore,
    required this.loserScore,
    this.notes,
  });
}

class DisputeMatchResultParams {
  final String challengeId;
  final String reason;
  final String? evidence;

  DisputeMatchResultParams({
    required this.challengeId,
    required this.reason,
    this.evidence,
  });
}

class GetMyChallengesParams {
  final int? page;
  final int? pageSize;

  GetMyChallengesParams({
    this.page,
    this.pageSize,
  });
}

class GetMatchRequestsParams {
  final String? matchType;
  final String? location;
  final String? skillLevel;
  final AgeGroup? ageGroup;
  final int? maxDistance;
  final bool? acceptWagers;
  final int? page;
  final int? pageSize;

  GetMatchRequestsParams({
    this.matchType,
    this.location,
    this.skillLevel,
    this.ageGroup,
    this.maxDistance,
    this.acceptWagers,
    this.page,
    this.pageSize,
  });
}

class CreateMatchRequestParams {
  final String? matchType;
  final String? preferredLocation;
  final String? skillLevel;
  final AgeGroup? ageGroup;
  final DateTime? preferredDateTime;
  final String? description;
  final int? maxDistance;
  final double? maxWagerAmount;
  final bool acceptWagers;
  final int expirationHours;

  CreateMatchRequestParams({
    this.matchType,
    this.preferredLocation,
    this.skillLevel,
    this.ageGroup,
    this.preferredDateTime,
    this.description,
    this.maxDistance,
    this.maxWagerAmount,
    required this.acceptWagers,
    required this.expirationHours,
  });
}

class GetMyMatchRequestsParams {
  final int? page;
  final int? pageSize;

  GetMyMatchRequestsParams({
    this.page,
    this.pageSize,
  });
}

class NoParams {
  const NoParams();
}
