import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:cached_network_image/cached_network_image.dart';
import '../../../../utils/color.dart';
import '../../../../core/config/theme.dart';
import '../../../../core/providers/theme_provider.dart' as theme_providers;

class MyMatchesScreen extends ConsumerStatefulWidget {
  const MyMatchesScreen({Key? key}) : super(key: key);

  @override
  ConsumerState<MyMatchesScreen> createState() => _MyMatchesScreenState();
}

class _MyMatchesScreenState extends ConsumerState<MyMatchesScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final isDark = ref.watch(theme_providers.isDarkModeProvider);
    final primaryColor = NextSportzTheme.getPrimaryColor(isDark);
    final accentColor = NextSportzTheme.getAccentColor(isDark);
    final secondaryColor = NextSportzTheme.getSecondaryColor(isDark);
    final lightBlue =
        isDark ? NextSportzTheme.darkBlue : NextSportzTheme.lightBlue;

    return Scaffold(
      backgroundColor: primaryColor,
      appBar: AppBar(
        backgroundColor: primaryColor,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => Navigator.of(context).pop(),
        ),
        title: const Text(
          'My Challenges',
          style: TextStyle(
            fontFamily: 'Gilroy_Bold',
            color: Colors.white,
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
      body: SafeArea(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 16),

            // Tab Bar
            Container(
              margin: const EdgeInsets.symmetric(horizontal: 16),
              decoration: BoxDecoration(
                color: lightBlue.withOpacity(0.3),
                borderRadius: BorderRadius.circular(16),
              ),
              child: TabBar(
                controller: _tabController,
                indicator: BoxDecoration(
                  borderRadius: BorderRadius.circular(16),
                  gradient: LinearGradient(
                    colors: [accentColor, accentColor.withOpacity(0.8)],
                  ),
                ),
                dividerColor: Colors.transparent,
                labelColor: Colors.white,
                unselectedLabelColor: Colors.white70,
                labelStyle: const TextStyle(
                  fontFamily: 'Gilroy_Bold',
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                ),
                unselectedLabelStyle: const TextStyle(
                  fontFamily: 'Gilroy_Medium',
                  fontSize: 12,
                ),
                tabs: const [
                  Tab(text: 'Active'),
                  Tab(text: 'Pending'),
                  Tab(text: 'Completed'),
                ],
              ),
            ),

            const SizedBox(height: 16),

            // Tab Views
            Expanded(
              child: TabBarView(
                controller: _tabController,
                children: [
                  _buildActiveMatches(),
                  _buildPendingMatches(),
                  _buildCompletedMatches(),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActiveMatches() {
    return SingleChildScrollView(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        children: [
          _buildMatchCard(
            title: 'vs Manchester United',
            subtitle: 'Today at 7:00 PM • Bhatbhateni Futsal',
            status: 'Active',
            statusColor: const Color(0xff00E676),
            matchType: '5v5',
            wager: 'Losers pay',
            timeLeft: '2h 30m',
            teamLogo: 'https://img.icons8.com/color/96/manchester-united.png',
            onTap: () => _showMatchDetails(context, 'Active Match'),
          ),
          _buildMatchCard(
            title: 'vs Liverpool FC',
            subtitle: 'Tomorrow at 6:00 PM • Dhumbarahi',
            status: 'Confirmed',
            statusColor: const Color(0xff2196F3),
            matchType: '7v7',
            wager: '+ NPR 500',
            timeLeft: '1d 2h',
            teamLogo: 'https://img.icons8.com/color/96/liverpool-fc.png',
            onTap: () => _showMatchDetails(context, 'Confirmed Match'),
          ),
        ],
      ),
    );
  }

  Widget _buildPendingMatches() {
    return SingleChildScrollView(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        children: [
          _buildMatchCard(
            title: 'vs Chelsea FC',
            subtitle: 'This weekend • Central Park',
            status: 'Pending',
            statusColor: Colors.orange,
            matchType: '5v5',
            wager: 'Losers pay',
            timeLeft: '3d 5h',
            teamLogo: 'https://img.icons8.com/color/96/chelsea-fc.png',
            onTap: () => _showMatchDetails(context, 'Pending Match'),
          ),
          _buildMatchCard(
            title: 'vs Arsenal FC',
            subtitle: 'Next week • Dhumbarahi',
            status: 'Awaiting Response',
            statusColor: Colors.yellow,
            matchType: '7v7',
            wager: '+ NPR 300',
            timeLeft: '5d 12h',
            teamLogo: 'https://img.icons8.com/color/96/arsenal-fc.png',
            onTap: () => _showMatchDetails(context, 'Pending Match'),
          ),
        ],
      ),
    );
  }

  Widget _buildCompletedMatches() {
    return SingleChildScrollView(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        children: [
          _buildMatchCard(
            title: 'vs Real Madrid',
            subtitle: 'Last week • Bhatbhateni Futsal',
            status: 'Won 3-1',
            statusColor: const Color(0xff00E676),
            matchType: '5v5',
            wager: 'Won NPR 500',
            timeLeft: 'Completed',
            teamLogo: 'https://img.icons8.com/color/96/real-madrid.png',
            showResult: true,
            onTap: () => _showMatchDetails(context, 'Completed Match'),
          ),
          _buildMatchCard(
            title: 'vs Barcelona',
            subtitle: '2 weeks ago • Central Park',
            status: 'Lost 1-2',
            statusColor: Colors.red,
            matchType: '7v7',
            wager: 'Lost NPR 300',
            timeLeft: 'Completed',
            teamLogo: 'https://img.icons8.com/color/96/fc-barcelona.png',
            showResult: true,
            onTap: () => _showMatchDetails(context, 'Completed Match'),
          ),
          _buildMatchCard(
            title: 'vs AC Milan',
            subtitle: '1 month ago • Dhumbarahi',
            status: 'Draw 2-2',
            statusColor: Colors.grey,
            matchType: '5v5',
            wager: 'Split fees',
            timeLeft: 'Completed',
            teamLogo: 'https://img.icons8.com/color/96/ac-milan.png',
            showResult: true,
            onTap: () => _showMatchDetails(context, 'Completed Match'),
          ),
        ],
      ),
    );
  }

  Widget _buildMatchCard({
    required String title,
    required String subtitle,
    required String status,
    required Color statusColor,
    required String matchType,
    required String wager,
    required String timeLeft,
    required VoidCallback onTap,
    String? teamLogo,
    bool showResult = false,
    bool isChallenge = false,
  }) {
    final isDark = ref.watch(theme_providers.isDarkModeProvider);
    final lightBlue =
        isDark ? NextSportzTheme.darkBlue : NextSportzTheme.lightBlue;
    final accentColor = NextSportzTheme.getAccentColor(isDark);

    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(18),
        child: Container(
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: lightBlue.withOpacity(0.5),
            borderRadius: BorderRadius.circular(18),
            border: Border.all(color: Colors.white.withOpacity(0.12), width: 1),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.1),
                blurRadius: 8,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header Row
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: statusColor.withOpacity(0.2),
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: statusColor.withOpacity(0.3)),
                    ),
                    child: Text(
                      status,
                      style: TextStyle(
                        color: statusColor,
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                        fontFamily: 'Gilroy_Bold',
                      ),
                    ),
                  ),
                  const Spacer(),
                  if (!isChallenge)
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 8,
                        vertical: 4,
                      ),
                      decoration: BoxDecoration(
                        color: Colors.white.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Text(
                        timeLeft,
                        style: const TextStyle(
                          color: Colors.white70,
                          fontSize: 11,
                          fontFamily: 'Gilroy_Medium',
                        ),
                      ),
                    ),
                ],
              ),
              const SizedBox(height: 12),

              // Title and subtitle with team logo
              Row(
                children: [
                  if (teamLogo != null) ...[
                    Container(
                      width: 32,
                      height: 32,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(16),
                        border: Border.all(
                          color: Colors.white.withOpacity(0.2),
                          width: 1,
                        ),
                      ),
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(16),
                        child: CachedNetworkImage(
                          imageUrl: teamLogo,
                          width: 32,
                          height: 32,
                          fit: BoxFit.cover,
                          placeholder: (context, url) => Container(
                            width: 32,
                            height: 32,
                            decoration: BoxDecoration(
                              color: Colors.white.withOpacity(0.1),
                              borderRadius: BorderRadius.circular(16),
                            ),
                            child: Center(
                              child: CircularProgressIndicator(
                                color: accentColor,
                                strokeWidth: 2,
                              ),
                            ),
                          ),
                          errorWidget: (context, url, error) => Container(
                            width: 32,
                            height: 32,
                            decoration: BoxDecoration(
                              color: Colors.white.withOpacity(0.1),
                              borderRadius: BorderRadius.circular(16),
                            ),
                            child: const Icon(
                              Icons.sports_soccer,
                              color: Colors.white70,
                              size: 16,
                            ),
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(width: 12),
                  ],
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          title,
                          style: const TextStyle(
                            fontFamily: 'Gilroy_Bold',
                            color: Colors.white,
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          subtitle,
                          style: TextStyle(
                            fontFamily: 'Gilroy_Medium',
                            color: Colors.white.withOpacity(0.7),
                            fontSize: 12,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),

              // Match details
              Row(
                children: [
                  _buildDetailChip(matchType, Icons.sports_soccer),
                  const SizedBox(width: 8),
                  _buildDetailChip(wager, Icons.monetization_on),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildDetailChip(String text, IconData icon) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.white.withOpacity(0.2)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            color: Colors.white70,
            size: 12,
          ),
          const SizedBox(width: 4),
          Text(
            text,
            style: const TextStyle(
              color: Colors.white70,
              fontSize: 11,
              fontFamily: 'Gilroy_Medium',
            ),
          ),
        ],
      ),
    );
  }

  void _showMatchDetails(BuildContext context, String matchType) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => _MatchDetailsBottomSheet(matchType: matchType),
    );
  }
}

class _MatchDetailsBottomSheet extends ConsumerWidget {
  final String matchType;

  const _MatchDetailsBottomSheet({required this.matchType});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final isDark = ref.watch(theme_providers.isDarkModeProvider);
    final secondaryColor = NextSportzTheme.getSecondaryColor(isDark);

    return Container(
      decoration: BoxDecoration(
        color: secondaryColor,
        borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: DraggableScrollableSheet(
        expand: false,
        initialChildSize: 0.7,
        maxChildSize: 0.9,
        minChildSize: 0.5,
        builder: (context, scrollController) => SingleChildScrollView(
          controller: scrollController,
          padding: const EdgeInsets.all(20),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Handle
              Center(
                child: Container(
                  width: 40,
                  height: 4,
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.3),
                    borderRadius: BorderRadius.circular(4),
                  ),
                ),
              ),
              const SizedBox(height: 20),

              Text(
                '$matchType Details',
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  fontFamily: 'Gilroy_Bold',
                ),
              ),
              const SizedBox(height: 20),

              Text(
                'Match details and actions will be implemented here.',
                style: TextStyle(
                  color: Colors.white.withOpacity(0.7),
                  fontSize: 16,
                  fontFamily: 'Gilroy_Medium',
                ),
              ),
              const SizedBox(height: 40),
            ],
          ),
        ),
      ),
    );
  }
}
