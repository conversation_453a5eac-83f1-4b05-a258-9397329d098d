import 'dart:math';
import '../dto/player_request_models.dart';
import '../dto/player_response_models.dart';
import 'player_datasource.dart';

/// Mock data source for player operations during development
class PlayerMockDataSource implements PlayerDatasource {
  final Random _random = Random();

  @override
  Future<PlayerProfileResponse> getPlayerProfile(String playerId) async {
    // Simulate network delay
    await Future.delayed(const Duration(milliseconds: 800));

    // Generate mock player data
    final mockPlayers = _generateMockPlayers();
    final player = mockPlayers.firstWhere(
      (p) => p.id == playerId,
      orElse: () => mockPlayers.first,
    );

    return player;
  }

  @override
  Future<PlayerProfileResponse> updatePlayerProfile(
    String playerId,
    UpdatePlayerProfileRequest request,
  ) async {
    // Simulate network delay
    await Future.delayed(const Duration(milliseconds: 600));

    // Get current player and update with new data
    final currentPlayer = await getPlayerProfile(playerId);

    return currentPlayer.copyWith(
      fullName: request.fullName ?? currentPlayer.fullName,
      dateOfBirth: request.dateOfBirth ?? currentPlayer.dateOfBirth,
      heightCm: request.heightCm ?? currentPlayer.heightCm,
      weightKg: request.weightKg ?? currentPlayer.weightKg,
      description: request.description ?? currentPlayer.description,
      photoUrl: request.photoUrl ?? currentPlayer.photoUrl,
      scoutingEnabled: request.scoutingEnabled ?? currentPlayer.scoutingEnabled,
      gameInfo: request.gameInfo != null
          ? PlayerGameInfoResponse(
              activeFoot: request.gameInfo!.activeFoot?.value,
              primaryPosition: request.gameInfo!.primaryPosition,
              playedPositions: request.gameInfo!.playedPositions,
              teamsPlayed: request.gameInfo!.teamsPlayed,
            )
          : currentPlayer.gameInfo,
    );
  }

  @override
  Future<PlayerRatingResponse> getPlayerRating(String playerId) async {
    // Simulate network delay
    await Future.delayed(const Duration(milliseconds: 700));

    // Generate mock rating data
    return _generateMockRatingResponse(playerId);
  }

  @override
  Future<void> submitRating(
    String playerId,
    SubmitRatingRequest request,
  ) async {
    // Simulate network delay
    await Future.delayed(const Duration(milliseconds: 500));

    // In a real implementation, this would save to the backend
    // For mock purposes, we just simulate success
    print('Mock: Rating submitted for player $playerId');
    print('Mock: Category: ${request.ratingCategory.value}');
    print('Mock: Defense: ${request.defense}, Shooting: ${request.shooting}');
  }

  List<PlayerProfileResponse> _generateMockPlayers() {
    return [
      PlayerProfileResponse(
        id: 'player_001',
        fullName: 'Lionel Messi',
        dateOfBirth: DateTime(1987, 6, 24),
        heightCm: 170,
        weightKg: 72,
        description:
            'Argentine professional footballer widely regarded as one of the greatest players of all time. Known for his exceptional dribbling skills, vision, and goal-scoring ability.',
        photoUrl: 'https://example.com/messi.jpg',
        scoutingEnabled: true,
        gameInfo: PlayerGameInfoResponse(
          activeFoot: 'Left',
          primaryPosition: 'Forward',
          playedPositions: ['Forward', 'Right Winger', 'Attacking Midfielder'],
          teamsPlayed: ['Barcelona', 'PSG', 'Inter Miami', 'Argentina'],
        ),
        averageRating: 94.2,
        totalRatings: 156,
        matchesPlayed: 1028,
        createdAt: DateTime(2020, 1, 1),
        updatedAt: DateTime(2024, 1, 15),
      ),
      PlayerProfileResponse(
        id: 'player_002',
        fullName: 'Cristiano Ronaldo',
        dateOfBirth: DateTime(1985, 2, 5),
        heightCm: 187,
        weightKg: 85,
        description:
            'Portuguese professional footballer considered one of the best players in the world. Known for his incredible athleticism, goal-scoring prowess, and leadership.',
        photoUrl: 'https://example.com/ronaldo.jpg',
        scoutingEnabled: true,
        gameInfo: PlayerGameInfoResponse(
          activeFoot: 'Right',
          primaryPosition: 'Forward',
          playedPositions: ['Forward', 'Left Winger', 'Striker'],
          teamsPlayed: [
            'Manchester United',
            'Real Madrid',
            'Juventus',
            'Al Nassr',
            'Portugal'
          ],
        ),
        averageRating: 93.8,
        totalRatings: 142,
        matchesPlayed: 1187,
        createdAt: DateTime(2020, 1, 1),
        updatedAt: DateTime(2024, 1, 10),
      ),
      PlayerProfileResponse(
        id: 'player_003',
        fullName: 'Kevin De Bruyne',
        dateOfBirth: DateTime(1991, 6, 28),
        heightCm: 181,
        weightKg: 76,
        description:
            'Belgian professional footballer known for his exceptional passing ability, vision, and technical skills. One of the best midfielders in the world.',
        photoUrl: 'https://example.com/debruyne.jpg',
        scoutingEnabled: true,
        gameInfo: PlayerGameInfoResponse(
          activeFoot: 'Right',
          primaryPosition: 'Midfielder',
          playedPositions: [
            'Central Midfielder',
            'Attacking Midfielder',
            'Right Midfielder'
          ],
          teamsPlayed: ['Manchester City', 'Chelsea', 'Wolfsburg', 'Belgium'],
        ),
        averageRating: 91.5,
        totalRatings: 89,
        matchesPlayed: 567,
        createdAt: DateTime(2020, 1, 1),
        updatedAt: DateTime(2024, 1, 12),
      ),
      PlayerProfileResponse(
        id: 'player_004',
        fullName: 'Virgil van Dijk',
        dateOfBirth: DateTime(1991, 7, 8),
        heightCm: 193,
        weightKg: 92,
        description:
            'Dutch professional footballer and one of the best defenders in the world. Known for his strength, aerial ability, and leadership.',
        photoUrl: 'https://example.com/vandijk.jpg',
        scoutingEnabled: true,
        gameInfo: PlayerGameInfoResponse(
          activeFoot: 'Right',
          primaryPosition: 'Defender',
          playedPositions: ['Center Back', 'Right Back'],
          teamsPlayed: ['Liverpool', 'Southampton', 'Celtic', 'Netherlands'],
        ),
        averageRating: 89.7,
        totalRatings: 67,
        matchesPlayed: 423,
        createdAt: DateTime(2020, 1, 1),
        updatedAt: DateTime(2024, 1, 8),
      ),
      PlayerProfileResponse(
        id: 'player_005',
        fullName: 'Erling Haaland',
        dateOfBirth: DateTime(2000, 7, 21),
        heightCm: 194,
        weightKg: 88,
        description:
            'Norwegian professional footballer known for his incredible goal-scoring record and physical presence. One of the most promising young strikers.',
        photoUrl: 'https://example.com/haaland.jpg',
        scoutingEnabled: true,
        gameInfo: PlayerGameInfoResponse(
          activeFoot: 'Left',
          primaryPosition: 'Striker',
          playedPositions: ['Striker', 'Center Forward'],
          teamsPlayed: [
            'Manchester City',
            'Borussia Dortmund',
            'RB Salzburg',
            'Norway'
          ],
        ),
        averageRating: 92.1,
        totalRatings: 78,
        matchesPlayed: 234,
        createdAt: DateTime(2020, 1, 1),
        updatedAt: DateTime(2024, 1, 20),
      ),
    ];
  }

  PlayerRatingResponse _generateMockRatingResponse(String playerId) {
    final baseRating = _getBaseRatingForPlayer(playerId);

    // Generate current rating with some variation
    final currentRating = CurrentRating(
      defense:
          (baseRating + _random.nextDouble() * 10 - 5).round().clamp(1, 100),
      shooting:
          (baseRating + _random.nextDouble() * 10 - 5).round().clamp(1, 100),
      passing:
          (baseRating + _random.nextDouble() * 10 - 5).round().clamp(1, 100),
      pace: (baseRating + _random.nextDouble() * 10 - 5).round().clamp(1, 100),
      physicality:
          (baseRating + _random.nextDouble() * 10 - 5).round().clamp(1, 100),
      dribbling:
          (baseRating + _random.nextDouble() * 10 - 5).round().clamp(1, 100),
      overall: baseRating + _random.nextDouble() * 10 - 5,
      publicVotes: _random.nextInt(50),
      opponentVotes: _random.nextInt(30),
      teammateVotes: _random.nextInt(40),
      scoutVotes: _random.nextInt(20),
      lastUpdated:
          DateTime.now().subtract(Duration(hours: _random.nextInt(24))),
    );

    // Generate recent votes
    final recentVotes = _generateMockRecentVotesForResponse(playerId);

    return PlayerRatingResponse(
      currentRating: currentRating,
      recentVotes: recentVotes,
    );
  }

  double _getBaseRatingForPlayer(String playerId) {
    switch (playerId) {
      case 'player_001':
        return 94.0; // Messi
      case 'player_002':
        return 93.0; // Ronaldo
      case 'player_003':
        return 91.0; // De Bruyne
      case 'player_004':
        return 89.0; // Van Dijk
      case 'player_005':
        return 92.0; // Haaland
      default:
        return 75.0 + _random.nextDouble() * 20;
    }
  }

  List<RecentVote> _generateMockRecentVotesForResponse(String playerId) {
    final votes = <RecentVote>[];
    final voterNames = [
      'Coach Smith',
      'Scout Johnson',
      'Teammate Wilson',
      'Opponent Brown',
      'Fan Davis',
      'Analyst Miller',
      'Manager Garcia',
      'Referee Rodriguez',
      'Sports Journalist',
      'Former Player',
      'Youth Coach',
      'Club Director'
    ];

    final categories = ['Self', 'Teammate', 'Opponent', 'Scout', 'Public'];

    for (int i = 0; i < 5; i++) {
      final baseRating = _getBaseRatingForPlayer(playerId);
      final category = categories[_random.nextInt(categories.length)];

      votes.add(RecentVote(
        voterId: 'voter_${DateTime.now().millisecondsSinceEpoch}_$i',
        voterName: voterNames[_random.nextInt(voterNames.length)],
        category: category,
        defense:
            (baseRating + _random.nextDouble() * 20 - 10).round().clamp(1, 100),
        shooting:
            (baseRating + _random.nextDouble() * 20 - 10).round().clamp(1, 100),
        passing:
            (baseRating + _random.nextDouble() * 20 - 10).round().clamp(1, 100),
        pace:
            (baseRating + _random.nextDouble() * 20 - 10).round().clamp(1, 100),
        physicality:
            (baseRating + _random.nextDouble() * 20 - 10).round().clamp(1, 100),
        dribbling:
            (baseRating + _random.nextDouble() * 20 - 10).round().clamp(1, 100),
        votedAt: DateTime.now().subtract(Duration(days: _random.nextInt(30))),
        comments: _random.nextBool() ? _generateMockComment() : null,
      ));
    }

    // Sort by voted date (most recent first)
    votes.sort((a, b) => b.votedAt.compareTo(a.votedAt));
    return votes;
  }

  List<PlayerRatingVote> _generateMockRecentVotes(
      String playerId, int totalRatings) {
    final votes = <PlayerRatingVote>[];
    final voterNames = [
      'Coach Smith',
      'Scout Johnson',
      'Teammate Wilson',
      'Opponent Brown',
      'Fan Davis',
      'Analyst Miller',
      'Manager Garcia',
      'Referee Rodriguez',
      'Sports Journalist',
      'Former Player',
      'Youth Coach',
      'Club Director'
    ];

    final categories = RatingCategory.values;

    for (int i = 0; i < 8; i++) {
      final baseRating = _getBaseRatingForPlayer(playerId);
      final category = categories[_random.nextInt(categories.length)];

      votes.add(PlayerRatingVote(
        id: 'vote_${DateTime.now().millisecondsSinceEpoch}_$i',
        ratingCategory: category.value,
        defense:
            (baseRating + _random.nextDouble() * 20 - 10).round().clamp(1, 100),
        shooting:
            (baseRating + _random.nextDouble() * 20 - 10).round().clamp(1, 100),
        passing:
            (baseRating + _random.nextDouble() * 20 - 10).round().clamp(1, 100),
        pace:
            (baseRating + _random.nextDouble() * 20 - 10).round().clamp(1, 100),
        physicality:
            (baseRating + _random.nextDouble() * 20 - 10).round().clamp(1, 100),
        dribbling:
            (baseRating + _random.nextDouble() * 20 - 10).round().clamp(1, 100),
        comments: _random.nextBool() ? _generateMockComment() : null,
        voterName: voterNames[_random.nextInt(voterNames.length)],
        createdAt: DateTime.now().subtract(Duration(days: _random.nextInt(30))),
      ));
    }

    // Sort by creation date (most recent first)
    votes.sort((a, b) => b.createdAt.compareTo(a.createdAt));
    return votes;
  }

  String _generateMockComment() {
    final comments = [
      'Excellent performance in the last match',
      'Great technical skills and vision',
      'Strong physical presence on the field',
      'Consistent performer throughout the season',
      'Shows great leadership qualities',
      'Impressive work rate and dedication',
      'Excellent decision making under pressure',
      'Great potential for future development',
      'Solid defensive contributions',
      'Creative attacking play',
    ];

    return comments[_random.nextInt(comments.length)];
  }
}
