import 'package:fpdart/fpdart.dart';
import '../../../../core/networking/app_error.dart';
import '../entities/player.dart';
import '../entities/player_rating.dart';
import '../../data/dto/player_request_models.dart';

/// Abstract class defining the contract for player repository operations
abstract class PlayerRepository {
  /// Get player profile by ID
  Future<Either<AppError, Player>> getPlayerProfile(String playerId);

  /// Update player profile
  Future<Either<AppError, Player>> updatePlayerProfile(
    String playerId,
    UpdatePlayerProfileRequest request,
  );

  /// Get player rating breakdown and recent votes
  Future<Either<AppError, PlayerRating>> getPlayerRating(String playerId);

  /// Submit rating for a player
  Future<Either<AppError, void>> submitRating(
    String playerId,
    SubmitRatingRequest request,
  );
}
