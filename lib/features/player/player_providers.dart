import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../core/networking/api_client.dart';
import 'data/datasources/player_datasource.dart';
import 'data/datasources/player_remote_datasource.dart';
import 'data/datasources/player_mock_datasource.dart';
import 'data/datasources/player_stats_mock_datasource.dart';
import 'data/repositories/player_repository_impl.dart';
import 'domain/repositories/player_repository.dart';
import 'domain/usecases/player_usecases.dart';
import '../../features/auth/data/repositories/auth_repository_provider.dart';
import '../../features/auth/domain/entities/user.dart';

/// Scaffold providers for Player feature following architecture/features/player

// Data source providers
final playerDataSourceProvider = Provider<PlayerDatasource>((ref) {
  // Use mock data source for development
  return PlayerRemoteDataSource(ref.watch(apiClientProvider));
});

// Repository providers
final playerRepositoryProvider = Provider<PlayerRepository>((ref) {
  final dataSource = ref.watch(playerDataSourceProvider);
  return PlayerRepositoryImpl(dataSource);
});

// Use case providers
final getPlayerProfileUseCaseProvider =
    Provider<GetPlayerProfileUseCase>((ref) {
  final repository = ref.watch(playerRepositoryProvider);
  return GetPlayerProfileUseCase(repository);
});

final updatePlayerProfileUseCaseProvider =
    Provider<UpdatePlayerProfileUseCase>((ref) {
  final repository = ref.watch(playerRepositoryProvider);
  return UpdatePlayerProfileUseCase(repository);
});

final getPlayerRatingUseCaseProvider = Provider<GetPlayerRatingUseCase>((ref) {
  final repository = ref.watch(playerRepositoryProvider);
  return GetPlayerRatingUseCase(repository);
});

final submitPlayerRatingUseCaseProvider =
    Provider<SubmitPlayerRatingUseCase>((ref) {
  final repository = ref.watch(playerRepositoryProvider);
  return SubmitPlayerRatingUseCase(repository);
});

// Current user's player ID provider
final currentPlayerIdProvider = FutureProvider<String>((ref) async {
  try {
    final authRepository = ref.read(authRepositoryProvider);
    final userResult = await authRepository.getCurrentUser();

    return userResult.fold(
      (error) => 'player_001',
      (user) => user!.playerId!,
    );
  } catch (e) {
    return 'player_001'; // Fallback to demo player
  }
});

// Feature providers
final playerProfileProvider =
    FutureProvider.family<dynamic, String>((ref, playerId) async {
  final useCase = ref.watch(getPlayerProfileUseCaseProvider);
  return await useCase(playerId);
});

final playerRatingProvider =
    FutureProvider.family<dynamic, String>((ref, playerId) async {
  final useCase = ref.watch(getPlayerRatingUseCaseProvider);
  return await useCase(playerId);
});

// Current user's player profile provider
final currentPlayerProfileProvider = FutureProvider<dynamic>((ref) async {
  final playerId = await ref.read(currentPlayerIdProvider.future);
  final useCase = ref.watch(getPlayerProfileUseCaseProvider);
  return await useCase(playerId);
});

// Current user's player rating provider
final currentPlayerRatingProvider = FutureProvider<dynamic>((ref) async {
  final playerId = await ref.read(currentPlayerIdProvider.future);
  final useCase = ref.watch(getPlayerRatingUseCaseProvider);
  return await useCase(playerId);
});

// Example: profile completion provider shape
final profileCompletionProvider =
    FutureProvider.family<double, String>((ref, playerId) async {
  // TODO: wire to repository when implemented in lib
  return 0.0;
});

// Player stats providers
final playerStatsDataSourceProvider =
    Provider<PlayerStatsMockDataSource>((ref) {
  return PlayerStatsMockDataSource();
});

final playerStatsProvider =
    FutureProvider.family<Map<String, dynamic>, String>((ref, playerId) async {
  final dataSource = ref.watch(playerStatsDataSourceProvider);
  return await dataSource.getPlayerChallengeStats(playerId);
});

// Current user's player stats provider
final currentPlayerStatsProvider =
    FutureProvider<Map<String, dynamic>>((ref) async {
  final playerId = await ref.read(currentPlayerIdProvider.future);
  final dataSource = ref.watch(playerStatsDataSourceProvider);
  return await dataSource.getPlayerChallengeStats(playerId);
});
