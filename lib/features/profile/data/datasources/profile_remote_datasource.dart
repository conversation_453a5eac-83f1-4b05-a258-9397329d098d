import '../../../../core/networking/api_client.dart';
import '../../../../core/networking/api_const.dart';
import '../../domain/entities/profile.dart';
import 'profile_datasource.dart';

class ProfileRemoteDataSource implements ProfileDatasource {
  final ApiClient apiClient;

  ProfileRemoteDataSource(this.apiClient);

  @override
  Future<Profile> getProfile() async {
    final response = await apiClient.get(ApiConst.meEndpoint);

    print('Profile API Response: $response');
    print('Profile API Response data: ${response['data']}');
    print(
        'Profile API Response dateOfBirth: ${response['data']?['dateOfBirth']}');

    // Convert the auth user response to profile
    return Profile(
      id: response['data']['id'] ?? '1',
      name: response['data']['fullName'] ?? 'User',
      email: response['data']['email'] ?? '',
      phoneNumber: response['data']['phoneNumber'] ?? '',
      profileImage: response['data']['profileImage'],
      role: response['data']['selectedRole'] ??
          response['data']['role'] ??
          'player',
      dateOfBirth: response['data']['dateOfBirth'] != null
          ? DateTime.parse(response['data']['dateOfBirth'])
          : null,
      preferences: {
        'notifications': true,
        'darkMode': false,
        'language': 'en',
      },
      createdAt: response['data']['createdAt'] != null
          ? DateTime.parse(response['data']['createdAt'])
          : DateTime.now().subtract(const Duration(days: 30)),
      updatedAt: response['data']['updatedAt'] != null
          ? DateTime.parse(response['data']['updatedAt'])
          : DateTime.now(),
      isActive: true,
    );
  }

  @override
  Future<Profile> updateProfile(Profile profile) async {
    // Send dateOfBirth and fullName to the updateProfileEndpoint
    // fullName is prefilled for security, dateOfBirth is the only user-editable field
    if (profile.dateOfBirth != null) {
      // Ensure the date includes timezone information by creating a UTC DateTime
      final utcDateOfBirth = DateTime.utc(
        profile.dateOfBirth!.year,
        profile.dateOfBirth!.month,
        profile.dateOfBirth!.day,
      );

      final requestData = {
        'fullName': profile.name, // Prefilled for security
        'dateOfBirth': utcDateOfBirth.toIso8601String(),
      };

      print('Updating profile with data: $requestData');
      print('Original date: ${profile.dateOfBirth}');
      print('UTC date: $utcDateOfBirth');
      print('Date of birth ISO string: ${utcDateOfBirth.toIso8601String()}');

      await apiClient.post(
        ApiConst.updateProfileEndpoint,
        data: requestData,
      );
    }

    // Return the updated profile
    return profile.copyWith(
      updatedAt: DateTime.now(),
    );
  }

  @override
  Future<void> uploadProfileImage(String imagePath) async {
    // TODO: Implement image upload
    // This would typically use FormData to upload the image
    await Future.delayed(const Duration(seconds: 1));
  }
}
