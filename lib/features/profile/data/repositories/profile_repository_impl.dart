import 'package:fpdart/fpdart.dart';
import '../../domain/entities/profile.dart';
import '../../domain/repositories/profile_repository.dart';
import '../datasources/profile_datasource.dart';

class ProfileRepositoryImpl implements ProfileRepository {
  final ProfileDatasource _profileDatasource;

  ProfileRepositoryImpl(this._profileDatasource);

  @override
  Future<Either<String, Profile>> getProfile(String userId) async {
    try {
      final profile = await _profileDatasource.getProfile();
      return Right(profile);
    } catch (e) {
      return Left(e.toString());
    }
  }

  @override
  Future<Either<String, Profile>> updateProfile(Profile profile) async {
    try {
      final updatedProfile = await _profileDatasource.updateProfile(profile);
      return Right(updatedProfile);
    } catch (e) {
      return Left(e.toString());
    }
  }

  @override
  Future<Either<String, void>> updateProfileImage(
    String userId,
    String imagePath,
  ) async {
    try {
      await _profileDatasource.uploadProfileImage(imagePath);
      return const Right(null);
    } catch (e) {
      return Left(e.toString());
    }
  }

  @override
  Future<Either<String, void>> updatePreferences(
    String userId,
    Map<String, dynamic> preferences,
  ) async {
    try {
      // TODO: Implement preferences update
      await Future.delayed(const Duration(seconds: 1));
      return const Right(null);
    } catch (e) {
      return Left(e.toString());
    }
  }

  @override
  Future<Either<String, void>> deleteProfile(String userId) async {
    try {
      // TODO: Implement profile deletion
      await Future.delayed(const Duration(seconds: 1));
      return const Right(null);
    } catch (e) {
      return Left(e.toString());
    }
  }
}
