import 'package:fpdart/fpdart.dart';
import '../entities/profile.dart';

abstract class ProfileRepository {
  Future<Either<String, Profile>> getProfile(String userId);
  Future<Either<String, Profile>> updateProfile(Profile profile);
  Future<Either<String, void>> updateProfileImage(
    String userId,
    String imagePath,
  );
  Future<Either<String, void>> updatePreferences(
    String userId,
    Map<String, dynamic> preferences,
  );
  Future<Either<String, void>> deleteProfile(String userId);
}

