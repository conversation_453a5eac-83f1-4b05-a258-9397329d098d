import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../utils/color.dart';
import '../screens/player_profile_details_screen.dart';
import '../../../player/player_providers.dart';
import '../../../player/domain/entities/player.dart';
import '../../../player/presentation/screens/player_profile_screen.dart';
import '../../../player/presentation/screens/player_profile_dialog_screen.dart';

class PlayerProfileCard extends ConsumerWidget {
  const PlayerProfileCard({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Use current user's player data
    final playerProfileAsync = ref.watch(currentPlayerProfileProvider);
    final playerStatsAsync = ref.watch(currentPlayerStatsProvider);

    return GestureDetector(
      onTap: () async {
        // Get current player ID and navigate to player profile dialog
        final playerId = await ref.read(currentPlayerIdProvider.future);
        if (context.mounted) {
          Navigator.of(context).push(
            MaterialPageRoute(
              builder: (context) =>
                  PlayerProfileDialogScreen(playerId: playerId),
            ),
          );
        }
      },
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(20),
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              const Color(0xffDA22FF).withOpacity(0.1),
              lightblue.withOpacity(0.3),
            ],
          ),
          border: Border.all(
            color: const Color(0xffDA22FF).withOpacity(0.3),
            width: 1,
          ),
        ),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: const Color(0xffDA22FF),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: const Icon(
                      Icons.sports_soccer,
                      color: Colors.white,
                      size: 24,
                    ),
                  ),
                  const SizedBox(width: 12),
                  const Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Player Profile',
                          style: TextStyle(
                            fontFamily: 'Gilroy_Bold',
                            color: Colors.white,
                            fontSize: 18,
                          ),
                        ),
                        Text(
                          'View your sports profile and performance',
                          style: TextStyle(
                            fontFamily: 'Gilroy_Medium',
                            color: Colors.grey,
                            fontSize: 12,
                          ),
                        ),
                      ],
                    ),
                  ),
                  const Icon(
                    Icons.arrow_forward_ios,
                    color: Colors.grey,
                    size: 16,
                  ),
                ],
              ),

              const SizedBox(height: 16),

              // Performance Stats Preview with real data
              playerStatsAsync.when(
                data: (stats) => _buildPerformanceStatsPreview(stats),
                loading: () => _buildPerformanceStatsPreview({}),
                error: (error, stack) => _buildPerformanceStatsPreview({}),
              ),

              const SizedBox(height: 12),

              // View More Button - always show for stats
              _buildViewMoreButton(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildPerformanceStatsPreview(Map<String, dynamic> stats) {
    if (stats.isEmpty) {
      return _buildLoadingStatsPreview();
    }

    final totalChallenges = stats['totalChallenges'] ?? 0;
    final challengesWon = stats['challengesWon'] ?? 0;
    final totalWagerWon = stats['totalWagerWon'] ?? 0;
    final winRate = stats['winRate'] ?? 0.0;

    return Row(
      children: [
        Expanded(
          child: _buildPreviewItem(
            'Challenges',
            totalChallenges.toString(),
            Icons.flash_on,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: _buildPreviewItem(
            'Wins',
            challengesWon.toString(),
            Icons.emoji_events,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: _buildPreviewItem(
            'Wager Won',
            '₹${(totalWagerWon / 1000).toStringAsFixed(1)}k',
            Icons.monetization_on,
          ),
        ),
      ],
    );
  }

  Widget _buildLoadingStatsPreview() {
    return Row(
      children: [
        Expanded(
          child: _buildPreviewItem('Challenges', '...', Icons.flash_on),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: _buildPreviewItem('Wins', '...', Icons.emoji_events),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: _buildPreviewItem('Wager Won', '...', Icons.monetization_on),
        ),
      ],
    );
  }

  Widget _buildPreviewItem(String label, String value, IconData icon) {
    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: Colors.black.withOpacity(0.2),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: Colors.white.withOpacity(0.1),
          width: 1,
        ),
      ),
      child: Column(
        children: [
          Icon(icon, color: const Color(0xffDA22FF), size: 16),
          const SizedBox(height: 4),
          Text(
            value,
            style: const TextStyle(
              fontFamily: 'Gilroy_Bold',
              color: Colors.white,
              fontSize: 12,
            ),
          ),
          Text(
            label,
            style: const TextStyle(
              fontFamily: 'Gilroy_Medium',
              color: Colors.grey,
              fontSize: 10,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildViewMoreButton() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.symmetric(vertical: 8),
      decoration: BoxDecoration(
        color: const Color(0xffDA22FF).withOpacity(0.2),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: const Color(0xffDA22FF).withOpacity(0.5),
          width: 1,
        ),
      ),
      child: const Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
            'View More Details',
            style: TextStyle(
              fontFamily: 'Gilroy_Medium',
              color: Color(0xffDA22FF),
              fontSize: 14,
            ),
          ),
          SizedBox(width: 4),
          Icon(
            Icons.arrow_forward,
            color: Color(0xffDA22FF),
            size: 16,
          ),
        ],
      ),
    );
  }
}
