import '../../domain/entities/team.dart';
import '../dto/team_dto.dart';
import '../dto/team_request_dto.dart';

/// Abstract class defining the contract for teams data operations
abstract class TeamsDatasource {
  /// Get all teams for the current user
  Future<List<Team>> getMyTeams();

  /// Get a specific team by ID
  Future<Team> getTeamById(String teamId);

  /// Create a new team
  Future<Team> createTeam({
    required String name,
    required String description,
    String? logo,
    String? slogan,
  });

  /// Update an existing team
  Future<Team> updateTeam({
    required String teamId,
    String? name,
    String? description,
    String? logo,
    String? slogan,
  });

  /// Delete a team
  Future<void> deleteTeam(String teamId);

  /// Invite a player to join the team
  Future<void> invitePlayer({
    required String teamId,
    required String playerId,
    String? message,
  });

  /// Accept a team invitation
  Future<void> acceptInvitation(String invitationId);

  /// Decline a team invitation
  Future<void> declineInvitation(String invitationId);

  /// Remove a member from the team
  Future<void> removeMember({
    required String teamId,
    required String memberId,
  });

  /// Update a member's role in the team
  Future<void> updateMemberRole({
    required String teamId,
    required String memberId,
    required String role,
  });

  /// Get pending invitations for the current user
  Future<List<TeamInvitation>> getPendingInvitations();

  /// Upload team logo
  Future<String> uploadTeamLogo(dynamic imageFile);
}
