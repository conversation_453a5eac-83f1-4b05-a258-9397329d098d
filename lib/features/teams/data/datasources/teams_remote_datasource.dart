import 'dart:io';
import 'package:dio/dio.dart';
import '../../domain/entities/team.dart';
import '../../../../core/networking/api_client.dart';
import '../../../../core/networking/api_const.dart';
import '../dto/team_dto.dart';
import '../dto/team_request_dto.dart';
import 'teams_datasource.dart';

/// Remote data source implementation for teams operations
class TeamsRemoteDataSource implements TeamsDatasource {
  final ApiClient apiClient;

  TeamsRemoteDataSource(this.apiClient);

  @override
  Future<List<Team>> getMyTeams() async {
    final response = await apiClient.get(ApiConst.myTeamsEndpoint);
    final List<dynamic> data = response['data'] ?? response;
    return data.map((json) {
      final dto = TeamDto.fromJson(json);
      return _mapDtoToEntity(dto);
    }).toList();
  }

  @override
  Future<Team> getTeamById(String teamId) async {
    final endpoint = ApiConst.teamDetailEndpoint.replaceAll('{teamId}', teamId);
    final response = await apiClient.get(endpoint);
    final dto = TeamDto.fromJson(response);
    return _mapDtoToEntity(dto);
  }

  @override
  Future<Team> createTeam({
    required String name,
    required String description,
    String? logo,
    String? slogan,
  }) async {
    final requestDto = CreateTeamRequestDto(
      name: name,
      description: description,
      logo: logo,
      slogan: slogan,
    );

    final response = await apiClient.post(
      ApiConst.teamsEndpoint,
      data: requestDto.toJson(),
    );
    final dto = TeamDto.fromJson(response);
    return _mapDtoToEntity(dto);
  }

  @override
  Future<Team> updateTeam({
    required String teamId,
    String? name,
    String? description,
    String? logo,
    String? slogan,
  }) async {
    final endpoint = ApiConst.teamDetailEndpoint.replaceAll('{teamId}', teamId);
    final requestDto = UpdateTeamRequestDto(
      name: name,
      description: description,
      logo: logo,
      slogan: slogan,
    );

    final response = await apiClient.put(
      endpoint,
      data: requestDto.toJson(),
    );
    final dto = TeamDto.fromJson(response);
    return _mapDtoToEntity(dto);
  }

  @override
  Future<void> deleteTeam(String teamId) async {
    final endpoint = ApiConst.teamDetailEndpoint.replaceAll('{teamId}', teamId);
    await apiClient.delete(endpoint);
  }

  @override
  Future<void> invitePlayer({
    required String teamId,
    required String playerId,
    String? message,
  }) async {
    final endpoint =
        ApiConst.teamInvitePlayerEndpoint.replaceAll('{teamId}', teamId);
    final requestDto = InvitePlayerRequestDto(
      playerId: playerId,
      message: message,
    );

    await apiClient.post(
      endpoint,
      data: requestDto.toJson(),
    );
  }

  @override
  Future<void> acceptInvitation(String invitationId) async {
    final endpoint = ApiConst.teamInvitationResponseEndpoint
        .replaceAll('{invitationId}', invitationId);
    final requestDto = InvitationResponseRequestDto(action: 'accept');

    await apiClient.post(
      endpoint,
      data: requestDto.toJson(),
    );
  }

  @override
  Future<void> declineInvitation(String invitationId) async {
    final endpoint = ApiConst.teamInvitationResponseEndpoint
        .replaceAll('{invitationId}', invitationId);
    final requestDto = InvitationResponseRequestDto(action: 'decline');

    await apiClient.post(
      endpoint,
      data: requestDto.toJson(),
    );
  }

  @override
  Future<void> removeMember({
    required String teamId,
    required String memberId,
  }) async {
    final endpoint = '/api/teams/$teamId/members/$memberId';

    await apiClient.delete(endpoint);
  }

  @override
  Future<void> updateMemberRole({
    required String teamId,
    required String memberId,
    required String role,
  }) async {
    final endpoint = ApiConst.teamMemberRoleEndpoint
        .replaceAll('{teamId}', teamId)
        .replaceAll('{memberId}', memberId);

    final requestDto = UpdateMemberRoleRequestDto(role: role);
    await apiClient.put(
      endpoint,
      data: requestDto.toJson(),
    );
  }

  @override
  Future<List<TeamInvitation>> getPendingInvitations() async {
    final response = await apiClient.get(ApiConst.teamInvitationsEndpoint);
    final List<dynamic> data = response['data'] ?? response;
    return data.map((json) {
      final dto = TeamInvitationDto.fromJson(json);
      return _mapInvitationDtoToEntity(dto);
    }).toList();
  }

  @override
  Future<String> uploadTeamLogo(dynamic imageFile) async {
    // Create FormData for file upload
    MultipartFile multipartFile;

    if (imageFile is File) {
      multipartFile = await MultipartFile.fromFile(
        imageFile.path,
        filename: 'team_logo_${DateTime.now().millisecondsSinceEpoch}.jpg',
      );
    } else {
      // For test cases or mock files
      multipartFile = MultipartFile.fromBytes(
        [0], // dummy bytes
        filename: 'team_logo_${DateTime.now().millisecondsSinceEpoch}.jpg',
      );
    }

    final formData = FormData.fromMap({
      'file': multipartFile,
      'type': 'team_logo',
    });

    final response = await apiClient.post(
      ApiConst.fileUploadEndpoint,
      data: formData,
    );

    return response['url'] ?? response['file_url'] ?? '';
  }

  // Helper methods to map DTOs to entities
  Team _mapDtoToEntity(TeamDto dto) {
    return Team(
      id: dto.id,
      name: dto.name,
      description: dto.description,
      logo: dto.logo,
      slogan: dto.slogan,
      createdBy: dto.createdBy,
      createdAt: DateTime.parse(dto.createdAt),
      updatedAt: DateTime.parse(dto.updatedAt),
      isActive: dto.isActive,
      members:
          dto.members.map((member) => _mapMemberDtoToEntity(member)).toList(),
      invitations: dto.invitations
          .map((invitation) => _mapInvitationDtoToEntity(invitation))
          .toList(),
      stats: _mapStatsDtoToEntity(dto.stats),
    );
  }

  TeamMember _mapMemberDtoToEntity(TeamMemberDto dto) {
    return TeamMember(
      id: dto.id,
      userId: dto.userId,
      name: dto.name,
      profileImage: dto.profileImage,
      position: dto.position,
      role: dto.role,
      joinedAt: DateTime.parse(dto.joinedAt),
      stats: _mapPlayerStatsDtoToEntity(dto.stats),
      isActive: dto.isActive,
    );
  }

  TeamInvitation _mapInvitationDtoToEntity(TeamInvitationDto dto) {
    return TeamInvitation(
      id: dto.id,
      teamId: dto.teamId,
      invitedUserId: dto.invitedUserId,
      invitedUserName: dto.invitedUserName,
      invitedUserEmail: dto.invitedUserEmail,
      invitedUserPhone: dto.invitedUserPhone,
      status: dto.status,
      createdAt: DateTime.parse(dto.createdAt),
      respondedAt:
          dto.respondedAt != null ? DateTime.parse(dto.respondedAt!) : null,
    );
  }

  TeamStats _mapStatsDtoToEntity(TeamStatsDto dto) {
    return TeamStats(
      totalMatches: dto.totalMatches,
      wins: dto.wins,
      losses: dto.losses,
      draws: dto.draws,
      winRate: dto.winRate,
      totalGoals: dto.totalGoals,
      goalsConceded: dto.goalsConceded,
      cleanSheets: dto.cleanSheets,
      averageGoalsPerMatch: dto.averageGoalsPerMatch,
    );
  }

  PlayerStats _mapPlayerStatsDtoToEntity(PlayerStatsDto dto) {
    return PlayerStats(
      matchesPlayed: dto.matchesPlayed,
      goals: dto.goals,
      assists: dto.assists,
      cleanSheets: dto.cleanSheets,
      rating: dto.rating,
      yellowCards: dto.yellowCards,
      redCards: dto.redCards,
      minutesPlayed: dto.minutesPlayed,
    );
  }
}
