class CreateTeamRequestDto {
  final String name;
  final String description;
  final String? logo;
  final String? slogan;

  const CreateTeamRequestDto({
    required this.name,
    required this.description,
    this.logo,
    this.slogan,
  });

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'description': description,
      if (logo != null) 'logo': logo,
      if (slogan != null) 'slogan': slogan,
    };
  }
}

class UpdateTeamRequestDto {
  final String? name;
  final String? description;
  final String? logo;
  final String? slogan;

  const UpdateTeamRequestDto({
    this.name,
    this.description,
    this.logo,
    this.slogan,
  });

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {};
    if (name != null) data['name'] = name;
    if (description != null) data['description'] = description;
    if (logo != null) data['logo'] = logo;
    if (slogan != null) data['slogan'] = slogan;
    return data;
  }
}

class InvitePlayerRequestDto {
  final String playerId;
  final String? message;

  const InvitePlayerRequestDto({
    required this.playerId,
    this.message,
  });

  Map<String, dynamic> toJson() {
    return {
      'player_id': playerId,
      if (message != null) 'message': message,
    };
  }
}

class UpdateMemberRoleRequestDto {
  final String role;

  const UpdateMemberRoleRequestDto({
    required this.role,
  });

  Map<String, dynamic> toJson() {
    return {
      'role': role,
    };
  }
}

class InvitationResponseRequestDto {
  final String action; // 'accept' or 'decline'

  const InvitationResponseRequestDto({
    required this.action,
  });

  Map<String, dynamic> toJson() {
    return {
      'action': action,
    };
  }
}
