import '../entities/team.dart';

abstract class TeamsRepository {
  Future<List<Team>> getMyTeams();
  Future<Team> getTeamById(String teamId);
  Future<Team> createTeam({
    required String name,
    required String description,
    String? logo,
    String? slogan,
  });
  Future<Team> updateTeam({
    required String teamId,
    String? name,
    String? description,
    String? logo,
    String? slogan,
  });
  Future<void> deleteTeam(String teamId);
  Future<void> invitePlayer({
    required String teamId,
    required String playerId,
    String? message,
  });
  Future<void> acceptInvitation(String invitationId);
  Future<void> declineInvitation(String invitationId);
  Future<void> removeMember({required String teamId, required String memberId});
  Future<void> updateMemberRole({
    required String teamId,
    required String memberId,
    required String role,
  });
  Future<List<TeamInvitation>> getPendingInvitations();
}
