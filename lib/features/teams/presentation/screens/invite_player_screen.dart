import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../utils/color.dart';
import '../../teams_providers.dart';

class InvitePlayerScreen extends ConsumerStatefulWidget {
  final String teamId;

  const InvitePlayerScreen({Key? key, required this.teamId}) : super(key: key);

  @override
  ConsumerState<InvitePlayerScreen> createState() => _InvitePlayerScreenState();
}

class _InvitePlayerScreenState extends ConsumerState<InvitePlayerScreen> {
  final _formKey = GlobalKey<FormState>();
  final _playerIdController = TextEditingController();
  final _emailController = TextEditingController();
  final _phoneController = TextEditingController();
  final _messageController = TextEditingController();
  bool _isLoading = false;
  String _inviteMethod = 'player_id'; // 'player_id', 'email', 'phone'

  @override
  void dispose() {
    _playerIdController.dispose();
    _emailController.dispose();
    _phoneController.dispose();
    _messageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: PrimeryColor,
      appBar: AppBar(
        backgroundColor: PrimeryColor,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => Navigator.of(context).pop(),
        ),
        title: const Text(
          'Invite Player',
          style: TextStyle(
            fontFamily: 'Gilroy_Bold',
            color: Colors.white,
            fontSize: 18,
          ),
        ),
        actions: [
          TextButton(
            onPressed: _isLoading ? null : _invitePlayer,
            child:
                _isLoading
                    ? const SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(
                        color: Color(0xffDA22FF),
                        strokeWidth: 2,
                      ),
                    )
                    : const Text(
                      'Send',
                      style: TextStyle(
                        fontFamily: 'Gilroy_Medium',
                        color: Color(0xffDA22FF),
                        fontSize: 16,
                      ),
                    ),
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(20),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header
              Center(
                child: Column(
                  children: [
                    Container(
                      width: 100,
                      height: 100,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(50),
                        color: const Color(0xffDA22FF).withOpacity(0.2),
                      ),
                      child: const Icon(
                        Icons.person_add,
                        color: Color(0xffDA22FF),
                        size: 50,
                      ),
                    ),
                    const SizedBox(height: 16),
                    const Text(
                      'Invite a Player',
                      style: TextStyle(
                        fontFamily: 'Gilroy_Bold',
                        color: Colors.white,
                        fontSize: 24,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Send an invitation to join your team',
                      style: TextStyle(
                        fontFamily: 'Gilroy_Medium',
                        color: grey,
                        fontSize: 16,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),

              const SizedBox(height: 32),

              // Invite Method Selection
              _buildInviteMethodSelector(),

              const SizedBox(height: 20),

              // Player ID Field
              if (_inviteMethod == 'player_id')
                _buildTextField(
                  controller: _playerIdController,
                  label: 'Player ID or Username',
                  icon: Icons.person,
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Please enter player ID or username';
                    }
                    return null;
                  },
                ),

              // Email Field
              if (_inviteMethod == 'email')
                _buildTextField(
                  controller: _emailController,
                  label: 'Email Address',
                  icon: Icons.email,
                  keyboardType: TextInputType.emailAddress,
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Please enter email address';
                    }
                    if (!RegExp(
                      r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$',
                    ).hasMatch(value)) {
                      return 'Please enter a valid email address';
                    }
                    return null;
                  },
                ),

              // Phone Field
              if (_inviteMethod == 'phone')
                _buildTextField(
                  controller: _phoneController,
                  label: 'Phone Number',
                  icon: Icons.phone,
                  keyboardType: TextInputType.phone,
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Please enter phone number';
                    }
                    return null;
                  },
                ),

              const SizedBox(height: 20),

              // Message Field
              _buildTextField(
                controller: _messageController,
                label: 'Invitation Message (Optional)',
                icon: Icons.message,
                maxLines: 4,
                validator: (value) {
                  return null; // Optional field
                },
              ),

              const SizedBox(height: 32),

              // Tips Section
              _buildTipsSection(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    required IconData icon,
    int maxLines = 1,
    TextInputType? keyboardType,
    String? Function(String?)? validator,
  }) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(15),
        color: lightblue.withOpacity(0.5),
        border: Border.all(color: Colors.white.withOpacity(0.1), width: 1),
      ),
      child: TextFormField(
        controller: controller,
        maxLines: maxLines,
        keyboardType: keyboardType,
        validator: validator,
        style: const TextStyle(
          fontFamily: 'Gilroy_Medium',
          color: Colors.white,
          fontSize: 16,
        ),
        decoration: InputDecoration(
          labelText: label,
          labelStyle: TextStyle(
            fontFamily: 'Gilroy_Medium',
            color: grey,
            fontSize: 14,
          ),
          prefixIcon: Icon(icon, color: const Color(0xffDA22FF), size: 20),
          border: InputBorder.none,
          contentPadding: const EdgeInsets.symmetric(
            horizontal: 20,
            vertical: 16,
          ),
        ),
      ),
    );
  }

  Widget _buildInviteMethodSelector() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(15),
        color: lightblue.withOpacity(0.5),
        border: Border.all(color: Colors.white.withOpacity(0.1), width: 1),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Invite Method',
            style: TextStyle(
              fontFamily: 'Gilroy_Bold',
              color: Colors.white,
              fontSize: 16,
            ),
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: _buildMethodOption(
                  'player_id',
                  'Player ID',
                  Icons.person,
                  _inviteMethod == 'player_id',
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: _buildMethodOption(
                  'email',
                  'Email',
                  Icons.email,
                  _inviteMethod == 'email',
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: _buildMethodOption(
                  'phone',
                  'Phone',
                  Icons.phone,
                  _inviteMethod == 'phone',
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildMethodOption(
    String value,
    String label,
    IconData icon,
    bool isSelected,
  ) {
    return InkWell(
      onTap: () {
        setState(() {
          _inviteMethod = value;
        });
      },
      borderRadius: BorderRadius.circular(8),
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 8),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8),
          color:
              isSelected
                  ? const Color(0xffDA22FF).withOpacity(0.2)
                  : Colors.transparent,
          border: Border.all(
            color:
                isSelected
                    ? const Color(0xffDA22FF)
                    : Colors.white.withOpacity(0.3),
            width: 1,
          ),
        ),
        child: Column(
          children: [
            Icon(
              icon,
              color:
                  isSelected
                      ? const Color(0xffDA22FF)
                      : Colors.white.withOpacity(0.7),
              size: 20,
            ),
            const SizedBox(height: 4),
            Text(
              label,
              style: TextStyle(
                fontFamily: 'Gilroy_Medium',
                color:
                    isSelected
                        ? const Color(0xffDA22FF)
                        : Colors.white.withOpacity(0.7),
                fontSize: 12,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTipsSection() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(20),
        color: const Color(0xffDA22FF).withOpacity(0.1),
        border: Border.all(
          color: const Color(0xffDA22FF).withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.lightbulb_outline,
                color: const Color(0xffDA22FF),
                size: 20,
              ),
              const SizedBox(width: 8),
              const Text(
                'Invitation Tips',
                style: TextStyle(
                  fontFamily: 'Gilroy_Bold',
                  color: Colors.white,
                  fontSize: 16,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          _buildTipItem(
            'Choose the appropriate invite method (Player ID, Email, or Phone)',
          ),
          _buildTipItem(
            'For existing users, use Player ID for instant invitation',
          ),
          _buildTipItem(
            'For new users, use Email or Phone to invite them to join the app',
          ),
          _buildTipItem(
            'Add a personal message to make the invitation more appealing',
          ),
          _buildTipItem(
            'The player will receive a notification about your invitation',
          ),
        ],
      ),
    );
  }

  Widget _buildTipItem(String text) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            width: 6,
            height: 6,
            margin: const EdgeInsets.only(top: 8, right: 12),
            decoration: const BoxDecoration(
              color: Color(0xffDA22FF),
              shape: BoxShape.circle,
            ),
          ),
          Expanded(
            child: Text(
              text,
              style: TextStyle(
                fontFamily: 'Gilroy_Medium',
                color: Colors.white.withOpacity(0.8),
                fontSize: 14,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _invitePlayer() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      String playerId = '';
      String? email;
      String? phone;

      switch (_inviteMethod) {
        case 'player_id':
          playerId = _playerIdController.text.trim();
          break;
        case 'email':
          email = _emailController.text.trim();
          break;
        case 'phone':
          phone = _phoneController.text.trim();
          break;
      }

      final useCase = ref.read(invitePlayerUseCaseProvider);
      await useCase(
        teamId: widget.teamId,
        playerId: playerId,
        message:
            _messageController.text.trim().isEmpty
                ? null
                : _messageController.text.trim(),
      );

      if (mounted) {
        Navigator.of(context).pop();
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Invitation sent successfully!'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error sending invitation: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }
}
