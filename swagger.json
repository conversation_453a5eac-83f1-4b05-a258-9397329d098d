{"openapi": "3.0.4", "info": {"title": "backend", "version": "1.0"}, "paths": {"/api/admin/dashboard/summary": {"get": {"tags": ["Admin"], "summary": "Get dashboard summary with key metrics", "parameters": [{"name": "FromDate", "in": "query", "schema": {"type": "string", "format": "date"}}, {"name": "ToDate", "in": "query", "schema": {"type": "string", "format": "date"}}], "responses": {"200": {"description": "OK"}}}}, "/api/admin/export/players": {"get": {"tags": ["Admin"], "summary": "Export players data as CSV or XLSX", "parameters": [{"name": "Format", "in": "query", "schema": {"type": "string"}}, {"name": "Search", "in": "query", "schema": {"type": "string"}}, {"name": "Position", "in": "query", "schema": {"type": "string"}}, {"name": "RegisteredAfter", "in": "query", "schema": {"type": "string", "format": "date"}}, {"name": "RegisteredBefore", "in": "query", "schema": {"type": "string", "format": "date"}}], "responses": {"200": {"description": "OK"}}}}, "/api/admin/export/venues": {"get": {"tags": ["Admin"], "summary": "Export venues data as CSV or XLSX", "parameters": [{"name": "Format", "in": "query", "schema": {"type": "string"}}, {"name": "City", "in": "query", "schema": {"type": "string"}}, {"name": "IsActive", "in": "query", "schema": {"type": "boolean"}}], "responses": {"200": {"description": "OK"}}}}, "/api/admin/export/tournaments": {"get": {"tags": ["Admin"], "summary": "Export tournaments data as CSV or XLSX", "parameters": [{"name": "Format", "in": "query", "schema": {"type": "string"}}, {"name": "Status", "in": "query", "schema": {"type": "string"}}, {"name": "StartDateAfter", "in": "query", "schema": {"type": "string", "format": "date"}}, {"name": "StartDateBefore", "in": "query", "schema": {"type": "string", "format": "date"}}], "responses": {"200": {"description": "OK"}}}}, "/auth/register": {"post": {"tags": ["<PERSON><PERSON>"], "summary": "Registers the user", "requestBody": {"description": "All details required", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RegisterRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RegisterRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/RegisterRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/auth/verify-otp": {"post": {"tags": ["<PERSON><PERSON>"], "summary": "Verifies the OTP", "requestBody": {"description": "Phonenumber and OTP required", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RegisterVerifyRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RegisterVerifyRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/RegisterVerifyRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/auth/login": {"post": {"tags": ["<PERSON><PERSON>"], "summary": "Login using Phonenumber, Role and Password", "requestBody": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LoginRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/LoginRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/LoginRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/auth/me": {"get": {"tags": ["<PERSON><PERSON>"], "summary": "Get user information, should be authorized", "responses": {"200": {"description": "OK"}}}}, "/auth/refresh": {"post": {"tags": ["<PERSON><PERSON>"], "summary": "Refresh and get new access token", "parameters": [{"name": "refreshToken", "in": "query", "description": "", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/auth/logout": {"post": {"tags": ["<PERSON><PERSON>"], "summary": "Logs out the user and cleans session", "parameters": [{"name": "refreshToken", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/auth/forgot-password": {"post": {"tags": ["<PERSON><PERSON>"], "summary": "Forgot password reset link is sent to registered email", "requestBody": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ForgotPasswordRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ForgotPasswordRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ForgotPasswordRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/auth/reset-password": {"post": {"tags": ["<PERSON><PERSON>"], "summary": "Resets password using token from email", "requestBody": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResetPasswordRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResetPasswordRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ResetPasswordRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/auth/update-profile": {"post": {"tags": ["<PERSON><PERSON>"], "summary": "Updates Fullname and Date of Birth of the user profile", "requestBody": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateProfileRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateProfileRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateProfileRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/booking/requests": {"get": {"tags": ["Booking"], "summary": "Get booking requests (for venue owners or requesters)", "parameters": [{"name": "Status", "in": "query", "schema": {"type": "string"}}, {"name": "VenueId", "in": "query", "schema": {"type": "string", "format": "uuid"}}, {"name": "Date", "in": "query", "schema": {"type": "string", "format": "date"}}, {"name": "Page", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "PageSize", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}, "put": {"tags": ["Booking"], "summary": "Accept or reject a booking request (venue owners)", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProcessBookingRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProcessBookingRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ProcessBookingRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/booking/requests/{requestId}": {"get": {"tags": ["Booking"], "summary": "Get specific booking request details", "parameters": [{"name": "requestId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK"}}}}, "/api/booking/checkin": {"post": {"tags": ["Booking"], "summary": "Check in to a booking", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CheckInRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CheckInRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CheckInRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/booking/checkout": {"post": {"tags": ["Booking"], "summary": "Check out from a booking", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CheckOutRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CheckOutRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CheckOutRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/booking/cancel": {"post": {"tags": ["Booking"], "summary": "Cancel a booking request", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CancelBookingRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CancelBookingRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CancelBookingRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/challenges": {"post": {"tags": ["Challenges"], "summary": "Create a new challenge", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateChallengeRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateChallengeRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateChallengeRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ChallengeResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ChallengeResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ChallengeResponse"}}}}}}, "get": {"tags": ["Challenges"], "summary": "Get challenges with filtering and pagination", "parameters": [{"name": "MatchType", "in": "query", "schema": {"type": "string"}}, {"name": "Location", "in": "query", "schema": {"type": "string"}}, {"name": "SkillLevel", "in": "query", "schema": {"type": "string"}}, {"name": "AgeGroup", "in": "query", "schema": {"type": "string"}}, {"name": "MaxWagerAmount", "in": "query", "schema": {"type": "number", "format": "double"}}, {"name": "<PERSON><PERSON><PERSON>", "in": "query", "schema": {"type": "boolean"}}, {"name": "FromDate", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "ToDate", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "Page", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "PageSize", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/PagedChallengesResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/PagedChallengesResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/PagedChallengesResponse"}}}}}}}, "/api/challenges/{challengeId}": {"get": {"tags": ["Challenges"], "summary": "Get detailed challenge information", "parameters": [{"name": "challengeId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ChallengeDetailResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ChallengeDetailResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ChallengeDetailResponse"}}}}}}, "put": {"tags": ["Challenges"], "summary": "Update challenge details", "parameters": [{"name": "challengeId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateChallengeRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateChallengeRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateChallengeRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ChallengeResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ChallengeResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ChallengeResponse"}}}}}}, "delete": {"tags": ["Challenges"], "summary": "Cancel a challenge", "parameters": [{"name": "challengeId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK"}}}}, "/api/challenges/respond": {"post": {"tags": ["Challenges"], "summary": "Respond to a challenge", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RespondToChallengeRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RespondToChallengeRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/RespondToChallengeRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ChallengeResponseResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ChallengeResponseResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ChallengeResponseResponse"}}}}}}}, "/api/challenges/result": {"post": {"tags": ["Challenges"], "summary": "Submit match result", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SubmitMatchResultRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SubmitMatchResultRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/SubmitMatchResultRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/challenges/dispute": {"post": {"tags": ["Challenges"], "summary": "Dispute match result", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DisputeMatchResultRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/DisputeMatchResultRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/DisputeMatchResultRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/challenges/stats": {"get": {"tags": ["Challenges"], "summary": "Get user's challenge statistics", "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ChallengeStatsResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ChallengeStatsResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ChallengeStatsResponse"}}}}}}}, "/api/challenges/suggestions": {"get": {"tags": ["Challenges"], "summary": "Get match suggestions for the current user", "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/MatchSuggestionResponse"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/MatchSuggestionResponse"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/MatchSuggestionResponse"}}}}}}}}, "/api/challenges/my-challenges": {"get": {"tags": ["Challenges"], "summary": "Get user's challenges (created and participated)", "parameters": [{"name": "page", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 1}}, {"name": "pageSize", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 20}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/PagedChallengesResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/PagedChallengesResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/PagedChallengesResponse"}}}}}}}, "/api/challenges/match-requests": {"post": {"tags": ["Challenges"], "summary": "Create a match request", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateMatchRequestRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateMatchRequestRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateMatchRequestRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/MatchRequestResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/MatchRequestResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/MatchRequestResponse"}}}}}}, "get": {"tags": ["Challenges"], "summary": "Get match requests with filtering and pagination", "parameters": [{"name": "MatchType", "in": "query", "schema": {"type": "string"}}, {"name": "Location", "in": "query", "schema": {"type": "string"}}, {"name": "SkillLevel", "in": "query", "schema": {"type": "string"}}, {"name": "AgeGroup", "in": "query", "schema": {"type": "string"}}, {"name": "MaxDistance", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "AcceptWagers", "in": "query", "schema": {"type": "boolean"}}, {"name": "Page", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "PageSize", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/PagedMatchRequestsResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/PagedMatchRequestsResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/PagedMatchRequestsResponse"}}}}}}}, "/api/challenges/my-match-requests": {"get": {"tags": ["Challenges"], "summary": "Get user's match requests", "parameters": [{"name": "page", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 1}}, {"name": "pageSize", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 20}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/PagedMatchRequestsResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/PagedMatchRequestsResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/PagedMatchRequestsResponse"}}}}}}}, "/api/messages": {"post": {"tags": ["Messages"], "summary": "Send a one-to-one message", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SendMessageRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SendMessageRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/SendMessageRequest"}}}}, "responses": {"200": {"description": "OK"}}}, "get": {"tags": ["Messages"], "summary": "Get messages in a conversation", "parameters": [{"name": "ConversationId", "in": "query", "schema": {"type": "string"}}, {"name": "WithUserId", "in": "query", "schema": {"type": "string", "format": "uuid"}}, {"name": "Page", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "PageSize", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/messages/{messageId}": {"get": {"tags": ["Messages"], "summary": "Get specific message by ID", "parameters": [{"name": "messageId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK"}}}}, "/api/messages/conversations": {"get": {"tags": ["Messages"], "summary": "Get user's conversations", "responses": {"200": {"description": "OK"}}}}, "/api/messages/mark-read": {"post": {"tags": ["Messages"], "summary": "Mark messages as read", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/MarkAsReadRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/MarkAsReadRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/MarkAsReadRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/messages/unread-count": {"get": {"tags": ["Messages"], "summary": "Get unread message count", "responses": {"200": {"description": "OK"}}}}, "/api/notifications": {"get": {"tags": ["Notifications"], "summary": "Get user notifications with optional filtering", "parameters": [{"name": "UnreadOnly", "in": "query", "schema": {"type": "boolean"}}, {"name": "Type", "in": "query", "schema": {"type": "string"}}, {"name": "Page", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "PageSize", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}, "post": {"tags": ["Notifications"], "summary": "Create a notification (system/admin only)", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateNotificationRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateNotificationRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateNotificationRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/notifications/bulk": {"post": {"tags": ["Notifications"], "summary": "Create bulk notifications (system/admin only)", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BulkCreateNotificationRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BulkCreateNotificationRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/BulkCreateNotificationRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/notifications/{notificationId}": {"get": {"tags": ["Notifications"], "summary": "Get specific notification by ID", "parameters": [{"name": "notificationId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK"}}}}, "/api/notifications/mark-read": {"post": {"tags": ["Notifications"], "summary": "Mark notifications as read", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/MarkNotificationsAsReadRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/MarkNotificationsAsReadRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/MarkNotificationsAsReadRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/notifications/mark-all-read": {"post": {"tags": ["Notifications"], "summary": "Mark all notifications as read", "responses": {"200": {"description": "OK"}}}}, "/api/notifications/summary": {"get": {"tags": ["Notifications"], "summary": "Get notification summary and counts", "responses": {"200": {"description": "OK"}}}}, "/api/notifications/unread-count": {"get": {"tags": ["Notifications"], "summary": "Get unread notification count", "responses": {"200": {"description": "OK"}}}}, "/api/players": {"get": {"tags": ["Players"], "summary": "Get all players with optional filtering", "parameters": [{"name": "Search", "in": "query", "schema": {"type": "string"}}, {"name": "Position", "in": "query", "schema": {"type": "string"}}, {"name": "Page", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "PageSize", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/players/{playerId}": {"get": {"tags": ["Players"], "summary": "Get player profile by ID", "parameters": [{"name": "playerId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK"}}}, "put": {"tags": ["Players"], "summary": "Update player profile (self only unless admin)", "parameters": [{"name": "playerId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdatePlayerRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdatePlayerRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdatePlayerRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/players/{playerId}/rating": {"post": {"tags": ["Players"], "summary": "Submit rating for a player", "parameters": [{"name": "playerId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SubmitRatingRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SubmitRatingRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/SubmitRatingRequest"}}}}, "responses": {"200": {"description": "OK"}}}, "get": {"tags": ["Players"], "summary": "Get player rating breakdown and recent votes", "parameters": [{"name": "playerId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK"}}}}, "/api/players/{playerId}/madcard": {"get": {"tags": ["Players"], "summary": "Generate or fetch player's MadSports card", "parameters": [{"name": "playerId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK"}}}}, "/api/teams": {"post": {"tags": ["Teams"], "summary": "Create a new team", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateTeamRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateTeamRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateTeamRequest"}}}}, "responses": {"200": {"description": "OK"}}}, "get": {"tags": ["Teams"], "summary": "Get all teams with optional filtering", "parameters": [{"name": "Search", "in": "query", "schema": {"type": "string"}}, {"name": "Page", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "PageSize", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/teams/{teamId}": {"get": {"tags": ["Teams"], "summary": "Get team details with members", "parameters": [{"name": "teamId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK"}}}, "put": {"tags": ["Teams"], "summary": "Update team details", "parameters": [{"name": "teamId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateTeamRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateTeamRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateTeamRequest"}}}}, "responses": {"200": {"description": "OK"}}}, "delete": {"tags": ["Teams"], "summary": "Delete team", "parameters": [{"name": "teamId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK"}}}}, "/api/teams/{teamId}/members": {"post": {"tags": ["Teams"], "summary": "Add member to team or assign role", "parameters": [{"name": "teamId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AddTeamMemberRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AddTeamMemberRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/AddTeamMemberRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/teams/{teamId}/members/{memberId}": {"put": {"tags": ["Teams"], "summary": "Update team member role or status", "parameters": [{"name": "teamId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}, {"name": "memberId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateTeamMemberRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateTeamMemberRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateTeamMemberRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/tournaments": {"get": {"tags": ["Tournaments"], "summary": "Get all tournaments with optional filtering", "parameters": [{"name": "Status", "in": "query", "schema": {"type": "string"}}, {"name": "Location", "in": "query", "schema": {"type": "string"}}, {"name": "Format", "in": "query", "schema": {"type": "string"}}, {"name": "Page", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "PageSize", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}, "post": {"tags": ["Tournaments"], "summary": "Create a new tournament", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateTournamentRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateTournamentRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateTournamentRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/tournaments/{tournamentId}": {"get": {"tags": ["Tournaments"], "summary": "Get tournament details with enrollments", "parameters": [{"name": "tournamentId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK"}}}, "put": {"tags": ["Tournaments"], "summary": "Update tournament details", "parameters": [{"name": "tournamentId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateTournamentRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateTournamentRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateTournamentRequest"}}}}, "responses": {"200": {"description": "OK"}}}, "delete": {"tags": ["Tournaments"], "summary": "Delete tournament", "parameters": [{"name": "tournamentId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK"}}}}, "/api/tournaments/{tournamentId}/enroll": {"post": {"tags": ["Tournaments"], "summary": "Enroll team or individual players into tournament", "parameters": [{"name": "tournamentId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/EnrollTournamentRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/EnrollTournamentRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/EnrollTournamentRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/tournaments/{tournamentId}/tiesheet": {"post": {"tags": ["Tournaments"], "summary": "Upload tie-sheet for tournament", "parameters": [{"name": "tournamentId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"File": {"type": "string", "format": "binary"}}}, "encoding": {"File": {"style": "form"}}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/venues": {"get": {"tags": ["Venues"], "summary": "Get all venues with optional filtering", "parameters": [{"name": "City", "in": "query", "schema": {"type": "string"}}, {"name": "AvailableOn", "in": "query", "schema": {"type": "string", "format": "date"}}, {"name": "Page", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "PageSize", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}, "post": {"tags": ["Venues"], "summary": "Create a new venue", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateVenueRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateVenueRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateVenueRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/venues/{venueId}": {"get": {"tags": ["Venues"], "summary": "Get venue details by ID", "parameters": [{"name": "venueId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK"}}}, "put": {"tags": ["Venues"], "summary": "Update venue details", "parameters": [{"name": "venueId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateVenueRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateVenueRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateVenueRequest"}}}}, "responses": {"200": {"description": "OK"}}}, "delete": {"tags": ["Venues"], "summary": "Delete venue", "parameters": [{"name": "venueId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK"}}}}, "/api/venues/{venueId}/book": {"post": {"tags": ["Venues"], "summary": "Book a venue time slot", "parameters": [{"name": "venueId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BookVenueRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BookVenueRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/BookVenueRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/venues/{venueId}/availability": {"get": {"tags": ["Venues"], "summary": "Get venue availability calendar", "parameters": [{"name": "venueId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}, {"name": "fromDate", "in": "query", "schema": {"type": "string", "format": "date"}}, {"name": "toDate", "in": "query", "schema": {"type": "string", "format": "date"}}], "responses": {"200": {"description": "OK"}}}}}, "components": {"schemas": {"ActiveFoot": {"enum": ["Left", "Right", "Both"], "type": "string"}, "AddTeamMemberRequest": {"type": "object", "properties": {"userId": {"type": "string", "format": "uuid"}, "role": {"type": "string", "nullable": true}}, "additionalProperties": false}, "BookVenueRequest": {"type": "object", "properties": {"date": {"type": "string", "format": "date"}, "startTime": {"type": "string", "format": "time"}, "endTime": {"type": "string", "format": "time"}, "teamId": {"type": "string", "format": "uuid", "nullable": true}, "notes": {"type": "string", "nullable": true}}, "additionalProperties": false}, "BulkCreateNotificationRequest": {"type": "object", "properties": {"userIds": {"type": "array", "items": {"type": "string", "format": "uuid"}, "nullable": true}, "title": {"type": "string", "nullable": true}, "body": {"type": "string", "nullable": true}, "type": {"type": "string", "nullable": true}, "actionUrl": {"type": "string", "nullable": true}, "metadata": {"type": "string", "nullable": true}, "relatedEntityId": {"type": "string", "format": "uuid", "nullable": true}, "relatedEntityType": {"type": "string", "nullable": true}}, "additionalProperties": false}, "CancelBookingRequest": {"type": "object", "properties": {"bookingRequestId": {"type": "string", "format": "uuid"}, "reason": {"type": "string", "nullable": true}}, "additionalProperties": false}, "ChallengeDetailResponse": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "challengerId": {"type": "string", "format": "uuid"}, "challengerName": {"type": "string", "nullable": true}, "challengerTeamId": {"type": "string", "format": "uuid", "nullable": true}, "challengerTeamName": {"type": "string", "nullable": true}, "opponentId": {"type": "string", "format": "uuid", "nullable": true}, "opponentName": {"type": "string", "nullable": true}, "opponentTeamId": {"type": "string", "format": "uuid", "nullable": true}, "opponentTeamName": {"type": "string", "nullable": true}, "matchType": {"type": "string", "nullable": true}, "ageGroup": {"type": "string", "nullable": true}, "skillLevel": {"type": "string", "nullable": true}, "location": {"type": "string", "nullable": true}, "venueId": {"type": "string", "format": "uuid", "nullable": true}, "venueName": {"type": "string", "nullable": true}, "proposedDateTime": {"type": "string", "format": "date-time"}, "alternativeDateTime1": {"type": "string", "format": "date-time", "nullable": true}, "alternativeDateTime2": {"type": "string", "format": "date-time", "nullable": true}, "wagerAmount": {"type": "number", "format": "double", "nullable": true}, "wagerType": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "rules": {"type": "string", "nullable": true}, "status": {"type": "string", "nullable": true}, "expiresAt": {"type": "string", "format": "date-time"}, "acceptedAt": {"type": "string", "format": "date-time", "nullable": true}, "completedAt": {"type": "string", "format": "date-time", "nullable": true}, "winnerId": {"type": "string", "format": "uuid", "nullable": true}, "winnerName": {"type": "string", "nullable": true}, "winnerTeamId": {"type": "string", "format": "uuid", "nullable": true}, "winnerTeamName": {"type": "string", "nullable": true}, "matchResult": {"type": "string", "nullable": true}, "isResultDisputed": {"type": "boolean"}, "responses": {"type": "array", "items": {"$ref": "#/components/schemas/ChallengeResponseResponse"}, "nullable": true}, "created": {"type": "string", "format": "date-time"}}, "additionalProperties": false}, "ChallengeResponse": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "challengerId": {"type": "string", "format": "uuid"}, "challengerName": {"type": "string", "nullable": true}, "challengerTeamId": {"type": "string", "format": "uuid", "nullable": true}, "challengerTeamName": {"type": "string", "nullable": true}, "opponentId": {"type": "string", "format": "uuid", "nullable": true}, "opponentName": {"type": "string", "nullable": true}, "opponentTeamId": {"type": "string", "format": "uuid", "nullable": true}, "opponentTeamName": {"type": "string", "nullable": true}, "matchType": {"type": "string", "nullable": true}, "ageGroup": {"type": "string", "nullable": true}, "skillLevel": {"type": "string", "nullable": true}, "location": {"type": "string", "nullable": true}, "venueId": {"type": "string", "format": "uuid", "nullable": true}, "venueName": {"type": "string", "nullable": true}, "proposedDateTime": {"type": "string", "format": "date-time"}, "alternativeDateTime1": {"type": "string", "format": "date-time", "nullable": true}, "alternativeDateTime2": {"type": "string", "format": "date-time", "nullable": true}, "wagerAmount": {"type": "number", "format": "double", "nullable": true}, "wagerType": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "rules": {"type": "string", "nullable": true}, "status": {"type": "string", "nullable": true}, "expiresAt": {"type": "string", "format": "date-time"}, "acceptedAt": {"type": "string", "format": "date-time", "nullable": true}, "completedAt": {"type": "string", "format": "date-time", "nullable": true}, "winnerId": {"type": "string", "format": "uuid", "nullable": true}, "winnerName": {"type": "string", "nullable": true}, "winnerTeamId": {"type": "string", "format": "uuid", "nullable": true}, "winnerTeamName": {"type": "string", "nullable": true}, "matchResult": {"type": "string", "nullable": true}, "isResultDisputed": {"type": "boolean"}, "responseCount": {"type": "integer", "format": "int32"}, "created": {"type": "string", "format": "date-time"}}, "additionalProperties": false}, "ChallengeResponseResponse": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "responderId": {"type": "string", "format": "uuid"}, "responderName": {"type": "string", "nullable": true}, "responderTeamId": {"type": "string", "format": "uuid", "nullable": true}, "responderTeamName": {"type": "string", "nullable": true}, "responseType": {"type": "string", "nullable": true}, "preferredDateTime": {"type": "string", "format": "date-time", "nullable": true}, "message": {"type": "string", "nullable": true}, "respondedAt": {"type": "string", "format": "date-time"}}, "additionalProperties": false}, "ChallengeStatsResponse": {"type": "object", "properties": {"totalChallenges": {"type": "integer", "format": "int32"}, "openChallenges": {"type": "integer", "format": "int32"}, "acceptedChallenges": {"type": "integer", "format": "int32"}, "completedChallenges": {"type": "integer", "format": "int32"}, "wonChallenges": {"type": "integer", "format": "int32"}, "lostChallenges": {"type": "integer", "format": "int32"}, "winRate": {"type": "number", "format": "double"}, "disputedChallenges": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "CheckInRequest": {"type": "object", "properties": {"bookingRequestId": {"type": "string", "format": "uuid"}}, "additionalProperties": false}, "CheckOutRequest": {"type": "object", "properties": {"bookingRequestId": {"type": "string", "format": "uuid"}}, "additionalProperties": false}, "CreateChallengeRequest": {"type": "object", "properties": {"matchType": {"type": "string", "nullable": true}, "ageGroup": {"type": "string", "nullable": true}, "skillLevel": {"type": "string", "nullable": true}, "location": {"type": "string", "nullable": true}, "venueId": {"type": "string", "format": "uuid", "nullable": true}, "proposedDateTime": {"type": "string", "format": "date-time"}, "alternativeDateTime1": {"type": "string", "format": "date-time", "nullable": true}, "alternativeDateTime2": {"type": "string", "format": "date-time", "nullable": true}, "wagerAmount": {"type": "number", "format": "double", "nullable": true}, "wagerType": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "rules": {"type": "string", "nullable": true}, "specificOpponentId": {"type": "string", "format": "uuid", "nullable": true}, "specificOpponentTeamId": {"type": "string", "format": "uuid", "nullable": true}, "challengerTeamId": {"type": "string", "format": "uuid", "nullable": true}, "expirationHours": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "CreateMatchRequestRequest": {"type": "object", "properties": {"matchType": {"type": "string", "nullable": true}, "preferredLocation": {"type": "string", "nullable": true}, "skillLevel": {"type": "string", "nullable": true}, "ageGroup": {"type": "string", "nullable": true}, "preferredDateTime": {"type": "string", "format": "date-time", "nullable": true}, "description": {"type": "string", "nullable": true}, "maxDistance": {"type": "integer", "format": "int32", "nullable": true}, "maxWagerAmount": {"type": "number", "format": "double", "nullable": true}, "acceptWagers": {"type": "boolean"}, "expirationHours": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "CreateNotificationRequest": {"type": "object", "properties": {"userId": {"type": "string", "format": "uuid"}, "title": {"type": "string", "nullable": true}, "body": {"type": "string", "nullable": true}, "type": {"type": "string", "nullable": true}, "actionUrl": {"type": "string", "nullable": true}, "metadata": {"type": "string", "nullable": true}, "relatedEntityId": {"type": "string", "format": "uuid", "nullable": true}, "relatedEntityType": {"type": "string", "nullable": true}}, "additionalProperties": false}, "CreateTeamRequest": {"type": "object", "properties": {"name": {"type": "string", "nullable": true}, "coachId": {"type": "string", "format": "uuid"}, "description": {"type": "string", "nullable": true}, "logoUrl": {"type": "string", "nullable": true}}, "additionalProperties": false}, "CreateTournamentRequest": {"type": "object", "properties": {"title": {"type": "string", "nullable": true}, "format": {"type": "string", "nullable": true}, "ageGroup": {"type": "string", "nullable": true}, "location": {"type": "string", "nullable": true}, "entryFee": {"type": "number", "format": "double", "nullable": true}, "awards": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "startDate": {"type": "string", "format": "date"}, "endDate": {"type": "string", "format": "date"}, "maxTeams": {"type": "integer", "format": "int32"}, "venueId": {"type": "string", "format": "uuid", "nullable": true}}, "additionalProperties": false}, "CreateVenueRequest": {"type": "object", "properties": {"name": {"type": "string", "nullable": true}, "address": {"type": "string", "nullable": true}, "city": {"type": "string", "nullable": true}, "state": {"type": "string", "nullable": true}, "country": {"type": "string", "nullable": true}, "postalCode": {"type": "string", "nullable": true}, "hourlyRate": {"type": "number", "format": "double"}, "description": {"type": "string", "nullable": true}, "amenities": {"type": "array", "items": {"type": "string"}, "nullable": true}, "images": {"type": "array", "items": {"type": "string"}, "nullable": true}}, "additionalProperties": false}, "DisputeMatchResultRequest": {"type": "object", "properties": {"challengeId": {"type": "string", "format": "uuid"}, "disputeReason": {"type": "string", "nullable": true}, "evidence": {"type": "string", "nullable": true}, "comments": {"type": "string", "nullable": true}}, "additionalProperties": false}, "EnrollTournamentRequest": {"type": "object", "properties": {"teamId": {"type": "string", "format": "uuid", "nullable": true}, "players": {"type": "array", "items": {"type": "string", "format": "uuid"}, "nullable": true}}, "additionalProperties": false}, "ForgotPasswordRequest": {"type": "object", "properties": {"email": {"type": "string", "nullable": true}}, "additionalProperties": false}, "LoginRequest": {"type": "object", "properties": {"phoneNumber": {"type": "string", "nullable": true}, "password": {"type": "string", "nullable": true}, "role": {"type": "string", "nullable": true}}, "additionalProperties": false}, "MarkAsReadRequest": {"type": "object", "properties": {"messageIds": {"type": "array", "items": {"type": "string", "format": "uuid"}, "nullable": true}}, "additionalProperties": false}, "MarkNotificationsAsReadRequest": {"type": "object", "properties": {"notificationIds": {"type": "array", "items": {"type": "string", "format": "uuid"}, "nullable": true}}, "additionalProperties": false}, "MatchRequestResponse": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "requesterId": {"type": "string", "format": "uuid"}, "requesterName": {"type": "string", "nullable": true}, "matchType": {"type": "string", "nullable": true}, "preferredLocation": {"type": "string", "nullable": true}, "skillLevel": {"type": "string", "nullable": true}, "ageGroup": {"type": "string", "nullable": true}, "preferredDateTime": {"type": "string", "format": "date-time", "nullable": true}, "description": {"type": "string", "nullable": true}, "isActive": {"type": "boolean"}, "expiresAt": {"type": "string", "format": "date-time"}, "maxDistance": {"type": "integer", "format": "int32", "nullable": true}, "maxWagerAmount": {"type": "number", "format": "double", "nullable": true}, "acceptWagers": {"type": "boolean"}, "created": {"type": "string", "format": "date-time"}}, "additionalProperties": false}, "MatchSuggestionResponse": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "type": {"type": "string", "nullable": true}, "matchType": {"type": "string", "nullable": true}, "location": {"type": "string", "nullable": true}, "proposedDateTime": {"type": "string", "format": "date-time", "nullable": true}, "opponentName": {"type": "string", "nullable": true}, "teamName": {"type": "string", "nullable": true}, "wagerAmount": {"type": "number", "format": "double", "nullable": true}, "compatibilityScore": {"type": "integer", "format": "int32"}, "description": {"type": "string", "nullable": true}}, "additionalProperties": false}, "PagedChallengesResponse": {"type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/components/schemas/ChallengeResponse"}, "nullable": true}, "total": {"type": "integer", "format": "int32"}, "page": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "totalPages": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "PagedMatchRequestsResponse": {"type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/components/schemas/MatchRequestResponse"}, "nullable": true}, "total": {"type": "integer", "format": "int32"}, "page": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "totalPages": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "PlayerGameInfoRequest": {"type": "object", "properties": {"activeFoot": {"$ref": "#/components/schemas/ActiveFoot"}, "primaryPosition": {"type": "string", "nullable": true}, "playedPositions": {"type": "array", "items": {"type": "string"}, "nullable": true}, "teamsPlayed": {"type": "array", "items": {"type": "string"}, "nullable": true}}, "additionalProperties": false}, "ProcessBookingRequest": {"type": "object", "properties": {"requestId": {"type": "string", "format": "uuid"}, "action": {"type": "string", "nullable": true}, "note": {"type": "string", "nullable": true}}, "additionalProperties": false}, "RatingCategory": {"enum": ["Self", "Public", "Opponent", "Teammate", "Scout"], "type": "string"}, "RegisterRequest": {"type": "object", "properties": {"fullName": {"type": "string", "nullable": true}, "email": {"type": "string", "nullable": true}, "password": {"type": "string", "nullable": true}, "role": {"type": "string", "nullable": true}, "phoneNumber": {"type": "string", "nullable": true}}, "additionalProperties": false}, "RegisterVerifyRequest": {"type": "object", "properties": {"phoneNumber": {"type": "string", "nullable": true}, "otp": {"type": "string", "nullable": true}}, "additionalProperties": false}, "ResetPasswordRequest": {"type": "object", "properties": {"email": {"type": "string", "nullable": true}, "token": {"type": "string", "nullable": true}, "newPassword": {"type": "string", "nullable": true}}, "additionalProperties": false}, "RespondToChallengeRequest": {"type": "object", "properties": {"challengeId": {"type": "string", "format": "uuid"}, "responseType": {"type": "string", "nullable": true}, "preferredDateTime": {"type": "string", "format": "date-time", "nullable": true}, "message": {"type": "string", "nullable": true}, "responderTeamId": {"type": "string", "format": "uuid", "nullable": true}}, "additionalProperties": false}, "SendMessageRequest": {"type": "object", "properties": {"toUserId": {"type": "string", "format": "uuid"}, "body": {"type": "string", "nullable": true}, "attachmentUrl": {"type": "string", "nullable": true}, "attachmentType": {"type": "string", "nullable": true}, "replyToMessageId": {"type": "string", "format": "uuid", "nullable": true}}, "additionalProperties": false}, "SubmitMatchResultRequest": {"type": "object", "properties": {"challengeId": {"type": "string", "format": "uuid"}, "winnerId": {"type": "string", "format": "uuid", "nullable": true}, "winnerTeamId": {"type": "string", "format": "uuid", "nullable": true}, "matchResult": {"type": "string", "nullable": true}, "comments": {"type": "string", "nullable": true}}, "additionalProperties": false}, "SubmitRatingRequest": {"type": "object", "properties": {"ratingCategory": {"$ref": "#/components/schemas/RatingCategory"}, "defense": {"type": "integer", "format": "int32"}, "shooting": {"type": "integer", "format": "int32"}, "passing": {"type": "integer", "format": "int32"}, "pace": {"type": "integer", "format": "int32"}, "physicality": {"type": "integer", "format": "int32"}, "dribbling": {"type": "integer", "format": "int32"}, "comments": {"type": "string", "nullable": true}}, "additionalProperties": false}, "UpdateChallengeRequest": {"type": "object", "properties": {"matchType": {"type": "string", "nullable": true}, "ageGroup": {"type": "string", "nullable": true}, "skillLevel": {"type": "string", "nullable": true}, "location": {"type": "string", "nullable": true}, "venueId": {"type": "string", "format": "uuid", "nullable": true}, "proposedDateTime": {"type": "string", "format": "date-time", "nullable": true}, "alternativeDateTime1": {"type": "string", "format": "date-time", "nullable": true}, "alternativeDateTime2": {"type": "string", "format": "date-time", "nullable": true}, "wagerAmount": {"type": "number", "format": "double", "nullable": true}, "wagerType": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "rules": {"type": "string", "nullable": true}, "status": {"type": "string", "nullable": true}}, "additionalProperties": false}, "UpdatePlayerRequest": {"type": "object", "properties": {"fullName": {"type": "string", "nullable": true}, "dateOfBirth": {"type": "string", "format": "date-time", "nullable": true}, "heightCm": {"type": "integer", "format": "int32", "nullable": true}, "weightKg": {"type": "integer", "format": "int32", "nullable": true}, "description": {"type": "string", "nullable": true}, "photoUrl": {"type": "string", "nullable": true}, "scoutingEnabled": {"type": "boolean", "nullable": true}, "gameInfo": {"$ref": "#/components/schemas/PlayerGameInfoRequest"}}, "additionalProperties": false}, "UpdateProfileRequest": {"type": "object", "properties": {"fullName": {"type": "string", "nullable": true}, "dateOfBirth": {"type": "string", "format": "date-time", "nullable": true}}, "additionalProperties": false}, "UpdateTeamMemberRequest": {"type": "object", "properties": {"memberId": {"type": "string", "format": "uuid"}, "role": {"type": "string", "nullable": true}, "isActive": {"type": "boolean"}}, "additionalProperties": false}, "UpdateTeamRequest": {"type": "object", "properties": {"name": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "logoUrl": {"type": "string", "nullable": true}}, "additionalProperties": false}, "UpdateTournamentRequest": {"type": "object", "properties": {"title": {"type": "string", "nullable": true}, "format": {"type": "string", "nullable": true}, "ageGroup": {"type": "string", "nullable": true}, "location": {"type": "string", "nullable": true}, "entryFee": {"type": "number", "format": "double", "nullable": true}, "awards": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "startDate": {"type": "string", "format": "date", "nullable": true}, "endDate": {"type": "string", "format": "date", "nullable": true}, "maxTeams": {"type": "integer", "format": "int32", "nullable": true}, "venueId": {"type": "string", "format": "uuid", "nullable": true}, "status": {"type": "string", "nullable": true}}, "additionalProperties": false}, "UpdateVenueRequest": {"type": "object", "properties": {"name": {"type": "string", "nullable": true}, "address": {"type": "string", "nullable": true}, "city": {"type": "string", "nullable": true}, "state": {"type": "string", "nullable": true}, "country": {"type": "string", "nullable": true}, "postalCode": {"type": "string", "nullable": true}, "hourlyRate": {"type": "number", "format": "double", "nullable": true}, "description": {"type": "string", "nullable": true}, "amenities": {"type": "array", "items": {"type": "string"}, "nullable": true}, "images": {"type": "array", "items": {"type": "string"}, "nullable": true}, "isActive": {"type": "boolean", "nullable": true}}, "additionalProperties": false}}, "securitySchemes": {"Bearer": {"type": "<PERSON><PERSON><PERSON><PERSON>", "description": "JWT Authorization header using the <PERSON><PERSON> scheme. Example: \"Authorization: Bearer {token}\"", "name": "Authorization", "in": "header"}}}, "security": [{"Bearer": []}]}