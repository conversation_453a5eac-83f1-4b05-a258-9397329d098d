// Mocks generated by <PERSON><PERSON>to 5.4.6 from annotations
// in nextsportz_v2/test/core/networking/session_manager_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i3;

import 'package:mockito/mockito.dart' as _i1;
import 'package:nextsportz_v2/core/local/token_storage.dart' as _i2;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

/// A class which mocks [TokenStorageService].
///
/// See the documentation for <PERSON><PERSON><PERSON>'s code generation for more information.
class MockTokenStorageService extends _i1.Mock
    implements _i2.TokenStorageService {
  MockTokenStorageService() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i3.Future<void> saveTokens({
    required String? accessToken,
    required String? refreshToken,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #saveTokens,
          [],
          {
            #accessToken: accessToken,
            #refreshToken: refreshToken,
          },
        ),
        returnValue: _i3.Future<void>.value(),
        returnValueForMissingStub: _i3.Future<void>.value(),
      ) as _i3.Future<void>);

  @override
  _i3.Future<Map<String, String>?> getTokens() => (super.noSuchMethod(
        Invocation.method(
          #getTokens,
          [],
        ),
        returnValue: _i3.Future<Map<String, String>?>.value(),
      ) as _i3.Future<Map<String, String>?>);

  @override
  _i3.Future<void> clearTokens() => (super.noSuchMethod(
        Invocation.method(
          #clearTokens,
          [],
        ),
        returnValue: _i3.Future<void>.value(),
        returnValueForMissingStub: _i3.Future<void>.value(),
      ) as _i3.Future<void>);

  @override
  _i3.Future<bool> hasTokens() => (super.noSuchMethod(
        Invocation.method(
          #hasTokens,
          [],
        ),
        returnValue: _i3.Future<bool>.value(false),
      ) as _i3.Future<bool>);
}
