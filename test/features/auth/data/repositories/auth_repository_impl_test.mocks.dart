// Mocks generated by <PERSON><PERSON>to 5.4.6 from annotations
// in nextsportz_v2/test/features/auth/data/repositories/auth_repository_impl_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i4;

import 'package:mockito/mockito.dart' as _i1;
import 'package:nextsportz_v2/core/local/token_storage.dart' as _i6;
import 'package:nextsportz_v2/features/auth/data/datasources/auth_datasource.dart'
    as _i3;
import 'package:nextsportz_v2/features/auth/data/dto/auth_request_models.dart'
    as _i5;
import 'package:nextsportz_v2/features/auth/data/dto/auth_response_models.dart'
    as _i2;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeSuccessResponse_0 extends _i1.SmartFake
    implements _i2.SuccessResponse {
  _FakeSuccessResponse_0(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeLoginResponse_1 extends _i1.SmartFake implements _i2.LoginResponse {
  _FakeLoginResponse_1(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeUserProfileResponse_2 extends _i1.SmartFake
    implements _i2.UserProfileResponse {
  _FakeUserProfileResponse_2(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeAuthTokenResponse_3 extends _i1.SmartFake
    implements _i2.AuthTokenResponse {
  _FakeAuthTokenResponse_3(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

/// A class which mocks [AuthDatasource].
///
/// See the documentation for Mockito's code generation for more information.
class MockAuthDatasource extends _i1.Mock implements _i3.AuthDatasource {
  MockAuthDatasource() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i4.Future<_i2.SuccessResponse> register(_i5.RegisterRequest? request) =>
      (super.noSuchMethod(
        Invocation.method(
          #register,
          [request],
        ),
        returnValue:
            _i4.Future<_i2.SuccessResponse>.value(_FakeSuccessResponse_0(
          this,
          Invocation.method(
            #register,
            [request],
          ),
        )),
      ) as _i4.Future<_i2.SuccessResponse>);

  @override
  _i4.Future<_i2.SuccessResponse> verifyOtp(
          _i5.RegisterVerifyRequest? request) =>
      (super.noSuchMethod(
        Invocation.method(
          #verifyOtp,
          [request],
        ),
        returnValue:
            _i4.Future<_i2.SuccessResponse>.value(_FakeSuccessResponse_0(
          this,
          Invocation.method(
            #verifyOtp,
            [request],
          ),
        )),
      ) as _i4.Future<_i2.SuccessResponse>);

  @override
  _i4.Future<_i2.LoginResponse> login(_i5.LoginRequest? request) =>
      (super.noSuchMethod(
        Invocation.method(
          #login,
          [request],
        ),
        returnValue: _i4.Future<_i2.LoginResponse>.value(_FakeLoginResponse_1(
          this,
          Invocation.method(
            #login,
            [request],
          ),
        )),
      ) as _i4.Future<_i2.LoginResponse>);

  @override
  _i4.Future<_i2.UserProfileResponse> getCurrentUser() => (super.noSuchMethod(
        Invocation.method(
          #getCurrentUser,
          [],
        ),
        returnValue: _i4.Future<_i2.UserProfileResponse>.value(
            _FakeUserProfileResponse_2(
          this,
          Invocation.method(
            #getCurrentUser,
            [],
          ),
        )),
      ) as _i4.Future<_i2.UserProfileResponse>);

  @override
  _i4.Future<_i2.AuthTokenResponse> refreshToken(String? refreshToken) =>
      (super.noSuchMethod(
        Invocation.method(
          #refreshToken,
          [refreshToken],
        ),
        returnValue:
            _i4.Future<_i2.AuthTokenResponse>.value(_FakeAuthTokenResponse_3(
          this,
          Invocation.method(
            #refreshToken,
            [refreshToken],
          ),
        )),
      ) as _i4.Future<_i2.AuthTokenResponse>);

  @override
  _i4.Future<_i2.SuccessResponse> logout({String? refreshToken}) =>
      (super.noSuchMethod(
        Invocation.method(
          #logout,
          [],
          {#refreshToken: refreshToken},
        ),
        returnValue:
            _i4.Future<_i2.SuccessResponse>.value(_FakeSuccessResponse_0(
          this,
          Invocation.method(
            #logout,
            [],
            {#refreshToken: refreshToken},
          ),
        )),
      ) as _i4.Future<_i2.SuccessResponse>);

  @override
  _i4.Future<_i2.SuccessResponse> forgotPassword(
          _i5.ForgotPasswordRequest? request) =>
      (super.noSuchMethod(
        Invocation.method(
          #forgotPassword,
          [request],
        ),
        returnValue:
            _i4.Future<_i2.SuccessResponse>.value(_FakeSuccessResponse_0(
          this,
          Invocation.method(
            #forgotPassword,
            [request],
          ),
        )),
      ) as _i4.Future<_i2.SuccessResponse>);

  @override
  _i4.Future<_i2.SuccessResponse> resetPassword(
          _i5.ResetPasswordRequest? request) =>
      (super.noSuchMethod(
        Invocation.method(
          #resetPassword,
          [request],
        ),
        returnValue:
            _i4.Future<_i2.SuccessResponse>.value(_FakeSuccessResponse_0(
          this,
          Invocation.method(
            #resetPassword,
            [request],
          ),
        )),
      ) as _i4.Future<_i2.SuccessResponse>);

  @override
  _i4.Future<_i2.SuccessResponse> updateProfile(
          _i5.UpdateProfileRequest? request) =>
      (super.noSuchMethod(
        Invocation.method(
          #updateProfile,
          [request],
        ),
        returnValue:
            _i4.Future<_i2.SuccessResponse>.value(_FakeSuccessResponse_0(
          this,
          Invocation.method(
            #updateProfile,
            [request],
          ),
        )),
      ) as _i4.Future<_i2.SuccessResponse>);
}

/// A class which mocks [TokenStorageService].
///
/// See the documentation for Mockito's code generation for more information.
class MockTokenStorageService extends _i1.Mock
    implements _i6.TokenStorageService {
  MockTokenStorageService() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i4.Future<void> saveTokens({
    required String? accessToken,
    required String? refreshToken,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #saveTokens,
          [],
          {
            #accessToken: accessToken,
            #refreshToken: refreshToken,
          },
        ),
        returnValue: _i4.Future<void>.value(),
        returnValueForMissingStub: _i4.Future<void>.value(),
      ) as _i4.Future<void>);

  @override
  _i4.Future<Map<String, String>?> getTokens() => (super.noSuchMethod(
        Invocation.method(
          #getTokens,
          [],
        ),
        returnValue: _i4.Future<Map<String, String>?>.value(),
      ) as _i4.Future<Map<String, String>?>);

  @override
  _i4.Future<void> clearTokens() => (super.noSuchMethod(
        Invocation.method(
          #clearTokens,
          [],
        ),
        returnValue: _i4.Future<void>.value(),
        returnValueForMissingStub: _i4.Future<void>.value(),
      ) as _i4.Future<void>);

  @override
  _i4.Future<bool> hasTokens() => (super.noSuchMethod(
        Invocation.method(
          #hasTokens,
          [],
        ),
        returnValue: _i4.Future<bool>.value(false),
      ) as _i4.Future<bool>);
}
