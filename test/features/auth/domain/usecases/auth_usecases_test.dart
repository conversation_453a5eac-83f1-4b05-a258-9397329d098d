import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:fpdart/fpdart.dart';
import 'package:nextsportz_v2/features/auth/domain/repositories/auth_repository.dart';
import 'package:nextsportz_v2/features/auth/domain/usecases/auth_usecases.dart';
import 'package:nextsportz_v2/features/auth/domain/entities/user.dart';

import 'auth_usecases_test.mocks.dart';

@GenerateMocks([AuthRepository])
void main() {
  group('AuthUseCases', () {
    late MockAuthRepository mockRepository;
    late LoginWithPhoneUseCase loginWithPhoneUseCase;
    late RegisterUseCase registerUseCase;
    late VerifyOtpUseCase verifyOtpUseCase;
    late LogoutUseCase logoutUseCase;
    late IsAuthenticatedUseCase isAuthenticatedUseCase;
    late ForgotPasswordUseCase forgotPasswordUseCase;
    late ResetPasswordUseCase resetPasswordUseCase;
    late UpdateProfileUseCase updateProfileUseCase;

    setUp(() {
      mockRepository = MockAuthRepository();
      loginWithPhoneUseCase = LoginWithPhoneUseCase(mockRepository);
      registerUseCase = RegisterUseCase(mockRepository);
      verifyOtpUseCase = VerifyOtpUseCase(mockRepository);
      logoutUseCase = LogoutUseCase(mockRepository);
      isAuthenticatedUseCase = IsAuthenticatedUseCase(mockRepository);
      forgotPasswordUseCase = ForgotPasswordUseCase(mockRepository);
      resetPasswordUseCase = ResetPasswordUseCase(mockRepository);
      updateProfileUseCase = UpdateProfileUseCase(mockRepository);
    });

    group('LoginWithPhoneUseCase', () {
      test('should login user successfully', () async {
        // Arrange
        const phoneNumber = '1234567890';
        const password = 'password123';
        const role = 'PLAYER';

        final user = User(
          id: '1',
          name: 'Test User',
          email: '<EMAIL>',
          phoneNumber: phoneNumber,
          role: role,
        );

        when(
          mockRepository.loginWithPhone(
            phoneNumber: phoneNumber,
            password: password,
            role: role,
          ),
        ).thenAnswer((_) async => Right(user));

        // Act
        final result = await loginWithPhoneUseCase(
          phoneNumber: phoneNumber,
          password: password,
          role: role,
        );

        // Assert
        expect(result.isRight(), isTrue);
        result.fold((error) => fail('Should not return error: $error'), (user) {
          expect(user.id, equals('1'));
          expect(user.name, equals('Test User'));
          expect(user.email, equals('<EMAIL>'));
          expect(user.phoneNumber, equals(phoneNumber));
          expect(user.role, equals(role));
        });

        verify(
          mockRepository.loginWithPhone(
            phoneNumber: phoneNumber,
            password: password,
            role: role,
          ),
        ).called(1);
      });

      test('should return error when login fails', () async {
        // Arrange
        const phoneNumber = '1234567890';
        const password = 'wrong_password';
        const role = 'PLAYER';
        const errorMessage = 'Invalid credentials';

        when(
          mockRepository.loginWithPhone(
            phoneNumber: phoneNumber,
            password: password,
            role: role,
          ),
        ).thenAnswer((_) async => Left(errorMessage));

        // Act
        final result = await loginWithPhoneUseCase(
          phoneNumber: phoneNumber,
          password: password,
          role: role,
        );

        // Assert
        expect(result.isLeft(), isTrue);
        result.fold(
          (error) => expect(error, equals(errorMessage)),
          (user) => fail('Should not return user'),
        );
      });
    });

    group('RegisterUseCase', () {
      test('should register user successfully', () async {
        // Arrange
        const fullName = 'Test User';
        const email = '<EMAIL>';
        const phoneNumber = '1234567890';
        const password = 'password123';
        const role = 'PLAYER';

        when(
          mockRepository.register(
            fullName: fullName,
            email: email,
            phoneNumber: phoneNumber,
            password: password,
            role: role,
          ),
        ).thenAnswer((_) async => const Right(true));

        // Act
        final result = await registerUseCase(
          fullName: fullName,
          email: email,
          phoneNumber: phoneNumber,
          password: password,
          role: role,
        );

        // Assert
        expect(result.isRight(), isTrue);
        result.fold(
          (error) => fail('Should not return error: $error'),
          (success) => expect(success, isTrue),
        );

        verify(
          mockRepository.register(
            fullName: fullName,
            email: email,
            phoneNumber: phoneNumber,
            password: password,
            role: role,
          ),
        ).called(1);
      });

      test('should return error when registration fails', () async {
        // Arrange
        const fullName = 'Test User';
        const email = '<EMAIL>';
        const phoneNumber = '1234567890';
        const password = 'password123';
        const role = 'PLAYER';
        const errorMessage = 'Registration failed';

        when(
          mockRepository.register(
            fullName: fullName,
            email: email,
            phoneNumber: phoneNumber,
            password: password,
            role: role,
          ),
        ).thenAnswer((_) async => Left(errorMessage));

        // Act
        final result = await registerUseCase(
          fullName: fullName,
          email: email,
          phoneNumber: phoneNumber,
          password: password,
          role: role,
        );

        // Assert
        expect(result.isLeft(), isTrue);
        result.fold(
          (error) => expect(error, equals(errorMessage)),
          (success) => fail('Should not return success'),
        );
      });
    });

    group('VerifyOtpUseCase', () {
      test('should verify OTP successfully', () async {
        // Arrange
        const phoneNumber = '1234567890';
        const otp = '123456';

        when(
          mockRepository.verifyOtp(phoneNumber: phoneNumber, otp: otp),
        ).thenAnswer((_) async => const Right(true));

        // Act
        final result = await verifyOtpUseCase(
          phoneNumber: phoneNumber,
          otp: otp,
        );

        // Assert
        expect(result.isRight(), isTrue);
        result.fold(
          (error) => fail('Should not return error: $error'),
          (success) => expect(success, isTrue),
        );

        verify(
          mockRepository.verifyOtp(phoneNumber: phoneNumber, otp: otp),
        ).called(1);
      });
    });

    group('LogoutUseCase', () {
      test('should logout successfully', () async {
        // Arrange
        when(
          mockRepository.logout(),
        ).thenAnswer((_) async => const Right(true));

        // Act
        final result = await logoutUseCase();

        // Assert
        expect(result.isRight(), isTrue);
        result.fold(
          (error) => fail('Should not return error: $error'),
          (success) => expect(success, isTrue),
        );

        verify(mockRepository.logout()).called(1);
      });

      test('should return error when logout fails', () async {
        // Arrange
        const errorMessage = 'Logout failed';

        when(
          mockRepository.logout(),
        ).thenAnswer((_) async => Left(errorMessage));

        // Act
        final result = await logoutUseCase();

        // Assert
        expect(result.isLeft(), isTrue);
        result.fold(
          (error) => expect(error, equals(errorMessage)),
          (success) => fail('Should not return success'),
        );
      });
    });

    group('IsAuthenticatedUseCase', () {
      test('should return true when user is authenticated', () async {
        // Arrange
        when(
          mockRepository.isAuthenticated(),
        ).thenAnswer((_) async => const Right(true));

        // Act
        final result = await isAuthenticatedUseCase();

        // Assert
        expect(result.isRight(), isTrue);
        result.fold(
          (error) => fail('Should not return error: $error'),
          (isAuthenticated) => expect(isAuthenticated, isTrue),
        );

        verify(mockRepository.isAuthenticated()).called(1);
      });

      test('should return false when user is not authenticated', () async {
        // Arrange
        when(
          mockRepository.isAuthenticated(),
        ).thenAnswer((_) async => const Right(false));

        // Act
        final result = await isAuthenticatedUseCase();

        // Assert
        expect(result.isRight(), isTrue);
        result.fold(
          (error) => fail('Should not return error: $error'),
          (isAuthenticated) => expect(isAuthenticated, isFalse),
        );
      });
    });

    group('ForgotPasswordUseCase', () {
      test('should send forgot password email successfully', () async {
        // Arrange
        const email = '<EMAIL>';

        when(
          mockRepository.forgotPassword(email: email),
        ).thenAnswer((_) async => const Right(true));

        // Act
        final result = await forgotPasswordUseCase(email: email);

        // Assert
        expect(result.isRight(), isTrue);
        result.fold(
          (error) => fail('Should not return error: $error'),
          (success) => expect(success, isTrue),
        );

        verify(mockRepository.forgotPassword(email: email)).called(1);
      });
    });

    group('ResetPasswordUseCase', () {
      test('should reset password successfully', () async {
        // Arrange
        const token = 'reset_token';
        const newPassword = 'new_password123';

        when(
          mockRepository.resetPassword(token: token, newPassword: newPassword),
        ).thenAnswer((_) async => const Right(true));

        // Act
        final result = await resetPasswordUseCase(
          token: token,
          newPassword: newPassword,
        );

        // Assert
        expect(result.isRight(), isTrue);
        result.fold(
          (error) => fail('Should not return error: $error'),
          (success) => expect(success, isTrue),
        );

        verify(
          mockRepository.resetPassword(token: token, newPassword: newPassword),
        ).called(1);
      });
    });

    group('UpdateProfileUseCase', () {
      test('should update profile successfully', () async {
        // Arrange
        const fullName = 'Updated Name';
        const email = '<EMAIL>';

        when(
          mockRepository.updateProfile(fullName: fullName, email: email),
        ).thenAnswer((_) async => const Right(true));

        // Act
        final result = await updateProfileUseCase(
          fullName: fullName,
          email: email,
        );

        // Assert
        expect(result.isRight(), isTrue);
        result.fold(
          (error) => fail('Should not return error: $error'),
          (success) => expect(success, isTrue),
        );

        verify(
          mockRepository.updateProfile(fullName: fullName, email: email),
        ).called(1);
      });
    });
  });
}
