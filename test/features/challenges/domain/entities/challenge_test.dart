import 'package:flutter_test/flutter_test.dart';
import 'package:nextsportz_v2/features/challenges/domain/entities/challenge.dart';

void main() {
  group('Challenge', () {
    test('should create challenge with all fields', () {
      final challenge = Challenge(
        id: 'challenge-123',
        challengerId: 'user-123',
        challengerName: '<PERSON>',
        challengerTeamId: 'team-123',
        challengerTeamName: 'FC Barcelona',
        opponentId: 'user-456',
        opponentName: '<PERSON>',
        opponentTeamId: 'team-456',
        opponentTeamName: 'Real Madrid',
        matchType: '5v5',
        ageGroup: '18-25',
        skillLevel: 'intermediate',
        location: 'Dhumbara<PERSON>al',
        venueId: 'venue-123',
        venueName: 'Dhumbarahi Futsal Center',
        proposedDateTime: DateTime(2024, 1, 15, 18, 0),
        alternativeDateTime1: DateTime(2024, 1, 16, 18, 0),
        alternativeDateTime2: DateTime(2024, 1, 17, 18, 0),
        wagerAmount: 500.0,
        wagerType: 'NPR',
        description: 'Looking for a competitive match',
        rules: 'Standard futsal rules',
        status: 'open',
        expiresAt: DateTime(2024, 1, 16, 18, 0),
        acceptedAt: null,
        completedAt: null,
        winnerId: null,
        winnerName: null,
        winnerTeamId: null,
        winnerTeamName: null,
        matchResult: null,
        isResultDisputed: false,
        responseCount: 0,
        created: DateTime(2024, 1, 15, 10, 0),
      );

      expect(challenge.id, 'challenge-123');
      expect(challenge.challengerId, 'user-123');
      expect(challenge.challengerName, 'John Doe');
      expect(challenge.challengerTeamName, 'FC Barcelona');
      expect(challenge.opponentId, 'user-456');
      expect(challenge.opponentName, 'Jane Smith');
      expect(challenge.matchType, '5v5');
      expect(challenge.location, 'Dhumbarahi Futsal');
      expect(challenge.wagerAmount, 500.0);
      expect(challenge.status, 'open');
      expect(challenge.isResultDisputed, false);
      expect(challenge.responseCount, 0);
    });

    test('should create challenge from JSON', () {
      final json = {
        'id': 'challenge-123',
        'challengerId': 'user-123',
        'challengerName': 'John Doe',
        'challengerTeamId': 'team-123',
        'challengerTeamName': 'FC Barcelona',
        'opponentId': 'user-456',
        'opponentName': 'Jane Smith',
        'opponentTeamId': 'team-456',
        'opponentTeamName': 'Real Madrid',
        'matchType': '5v5',
        'ageGroup': '18-25',
        'skillLevel': 'intermediate',
        'location': 'Dhumbarahi Futsal',
        'venueId': 'venue-123',
        'venueName': 'Dhumbarahi Futsal Center',
        'proposedDateTime': '2024-01-15T18:00:00.000Z',
        'alternativeDateTime1': '2024-01-16T18:00:00.000Z',
        'alternativeDateTime2': '2024-01-17T18:00:00.000Z',
        'wagerAmount': 500.0,
        'wagerType': 'NPR',
        'description': 'Looking for a competitive match',
        'rules': 'Standard futsal rules',
        'status': 'open',
        'expiresAt': '2024-01-16T18:00:00.000Z',
        'acceptedAt': null,
        'completedAt': null,
        'winnerId': null,
        'winnerName': null,
        'winnerTeamId': null,
        'winnerTeamName': null,
        'matchResult': null,
        'isResultDisputed': false,
        'responseCount': 0,
        'created': '2024-01-15T10:00:00.000Z',
      };

      final challenge = Challenge.fromJson(json);

      expect(challenge.id, 'challenge-123');
      expect(challenge.challengerId, 'user-123');
      expect(challenge.challengerName, 'John Doe');
      expect(challenge.challengerTeamName, 'FC Barcelona');
      expect(challenge.opponentId, 'user-456');
      expect(challenge.opponentName, 'Jane Smith');
      expect(challenge.matchType, '5v5');
      expect(challenge.location, 'Dhumbarahi Futsal');
      expect(challenge.wagerAmount, 500.0);
      expect(challenge.status, 'open');
      expect(challenge.isResultDisputed, false);
      expect(challenge.responseCount, 0);
    });

    test('should convert to JSON correctly', () {
      final challenge = Challenge(
        id: 'challenge-123',
        challengerId: 'user-123',
        challengerName: 'John Doe',
        matchType: '5v5',
        location: 'Dhumbarahi Futsal',
        proposedDateTime: DateTime(2024, 1, 15, 18, 0),
        expiresAt: DateTime(2024, 1, 16, 18, 0),
        isResultDisputed: false,
        responseCount: 0,
        created: DateTime(2024, 1, 15, 10, 0),
      );

      final json = challenge.toJson();

      expect(json['id'], 'challenge-123');
      expect(json['challengerId'], 'user-123');
      expect(json['challengerName'], 'John Doe');
      expect(json['matchType'], '5v5');
      expect(json['location'], 'Dhumbarahi Futsal');
      expect(json['proposedDateTime'], '2024-01-15T18:00:00.000');
      expect(json['expiresAt'], '2024-01-16T18:00:00.000');
      expect(json['isResultDisputed'], false);
      expect(json['responseCount'], 0);
    });

    test('should copy with new values', () {
      final original = Challenge(
        id: 'challenge-123',
        challengerId: 'user-123',
        challengerName: 'John Doe',
        matchType: '5v5',
        location: 'Dhumbarahi Futsal',
        proposedDateTime: DateTime(2024, 1, 15, 18, 0),
        expiresAt: DateTime(2024, 1, 16, 18, 0),
        isResultDisputed: false,
        responseCount: 0,
        created: DateTime(2024, 1, 15, 10, 0),
      );

      final updated = original.copyWith(
        status: 'accepted',
        opponentId: 'user-456',
        opponentName: 'Jane Smith',
        acceptedAt: DateTime(2024, 1, 15, 19, 0),
      );

      expect(updated.id, 'challenge-123');
      expect(updated.status, 'accepted');
      expect(updated.opponentId, 'user-456');
      expect(updated.opponentName, 'Jane Smith');
      expect(updated.acceptedAt, DateTime(2024, 1, 15, 19, 0));
      expect(updated.challengerName, 'John Doe'); // Unchanged
    });

    test('should compute status properties correctly', () {
      final now = DateTime.now();
      final future = now.add(const Duration(hours: 1));
      final past = now.subtract(const Duration(hours: 1));

      final openChallenge = Challenge(
        id: 'challenge-1',
        challengerId: 'user-123',
        status: 'open',
        proposedDateTime: future,
        expiresAt: future,
        isResultDisputed: false,
        responseCount: 0,
        created: now,
      );

      final acceptedChallenge = Challenge(
        id: 'challenge-2',
        challengerId: 'user-123',
        status: 'accepted',
        proposedDateTime: future,
        expiresAt: future,
        isResultDisputed: false,
        responseCount: 0,
        created: now,
      );

      final completedChallenge = Challenge(
        id: 'challenge-3',
        challengerId: 'user-123',
        status: 'completed',
        proposedDateTime: past,
        expiresAt: past,
        isResultDisputed: false,
        responseCount: 0,
        created: now,
      );

      final expiredChallenge = Challenge(
        id: 'challenge-4',
        challengerId: 'user-123',
        status: 'open',
        proposedDateTime: past,
        expiresAt: past,
        isResultDisputed: false,
        responseCount: 0,
        created: now,
      );

      expect(openChallenge.isOpen, true);
      expect(openChallenge.isAccepted, false);
      expect(openChallenge.isCompleted, false);
      expect(openChallenge.isExpired, false);

      expect(acceptedChallenge.isOpen, false);
      expect(acceptedChallenge.isAccepted, true);
      expect(acceptedChallenge.isCompleted, false);
      expect(acceptedChallenge.isExpired, false);

      expect(completedChallenge.isOpen, false);
      expect(completedChallenge.isAccepted, false);
      expect(completedChallenge.isCompleted, true);
      expect(completedChallenge.isExpired, true);

      expect(expiredChallenge.isOpen, false);
      expect(expiredChallenge.isAccepted, false);
      expect(expiredChallenge.isCompleted, false);
      expect(expiredChallenge.isExpired, true);
    });

    test('should provide correct display text', () {
      final challenge = Challenge(
        id: 'challenge-123',
        challengerId: 'user-123',
        challengerName: 'John Doe',
        challengerTeamName: 'FC Barcelona',
        opponentName: 'Jane Smith',
        opponentTeamName: 'Real Madrid',
        matchType: '5v5',
        location: 'Dhumbarahi Futsal',
        wagerAmount: 500.0,
        wagerType: 'NPR',
        status: 'open',
        proposedDateTime: DateTime(2024, 1, 15, 18, 0),
        expiresAt: DateTime(2024, 1, 16, 18, 0),
        isResultDisputed: false,
        responseCount: 0,
        created: DateTime(2024, 1, 15, 10, 0),
      );

      expect(challenge.displayName, 'John Doe');
      expect(challenge.opponentDisplayName, 'Jane Smith');
      expect(challenge.teamDisplayName, 'FC Barcelona');
      expect(challenge.opponentTeamDisplayName, 'Real Madrid');
      expect(challenge.wagerDisplayText, '500 NPR');
      expect(challenge.statusDisplayText, 'Open');
      expect(challenge.matchTypeDisplayText, '5 vs 5');
    });

    test('should handle null values in display text', () {
      final challenge = Challenge(
        id: 'challenge-123',
        challengerId: 'user-123',
        matchType: '5v5',
        location: 'Dhumbarahi Futsal',
        wagerAmount: null,
        status: 'open',
        proposedDateTime: DateTime(2024, 1, 15, 18, 0),
        expiresAt: DateTime(2024, 1, 16, 18, 0),
        isResultDisputed: false,
        responseCount: 0,
        created: DateTime(2024, 1, 15, 10, 0),
      );

      expect(challenge.displayName, 'Unknown Player');
      expect(challenge.opponentDisplayName, 'TBD');
      expect(challenge.teamDisplayName, 'Individual');
      expect(challenge.opponentTeamDisplayName, 'TBD');
      expect(challenge.wagerDisplayText, 'No wager');
    });
  });

  group('ChallengeResponse', () {
    test('should create challenge response with all fields', () {
      final response = ChallengeResponse(
        id: 'response-123',
        responderId: 'user-456',
        responderName: 'Jane Smith',
        responderTeamId: 'team-456',
        responderTeamName: 'Real Madrid',
        responseType: 'accepted',
        preferredDateTime: DateTime(2024, 1, 15, 19, 0),
        message: 'I accept the challenge!',
        respondedAt: DateTime(2024, 1, 15, 17, 0),
      );

      expect(response.id, 'response-123');
      expect(response.responderId, 'user-456');
      expect(response.responderName, 'Jane Smith');
      expect(response.responderTeamName, 'Real Madrid');
      expect(response.responseType, 'accepted');
      expect(response.message, 'I accept the challenge!');
    });

    test('should create challenge response from JSON', () {
      final json = {
        'id': 'response-123',
        'responderId': 'user-456',
        'responderName': 'Jane Smith',
        'responderTeamId': 'team-456',
        'responderTeamName': 'Real Madrid',
        'responseType': 'accepted',
        'preferredDateTime': '2024-01-15T19:00:00.000Z',
        'message': 'I accept the challenge!',
        'respondedAt': '2024-01-15T17:00:00.000Z',
      };

      final response = ChallengeResponse.fromJson(json);

      expect(response.id, 'response-123');
      expect(response.responderId, 'user-456');
      expect(response.responderName, 'Jane Smith');
      expect(response.responderTeamName, 'Real Madrid');
      expect(response.responseType, 'accepted');
      expect(response.message, 'I accept the challenge!');
    });

    test('should compute response type properties correctly', () {
      final acceptedResponse = ChallengeResponse(
        id: 'response-1',
        responderId: 'user-456',
        responseType: 'accepted',
        respondedAt: DateTime.now(),
      );

      final declinedResponse = ChallengeResponse(
        id: 'response-2',
        responderId: 'user-456',
        responseType: 'declined',
        respondedAt: DateTime.now(),
      );

      final counterOfferResponse = ChallengeResponse(
        id: 'response-3',
        responderId: 'user-456',
        responseType: 'counter_offer',
        respondedAt: DateTime.now(),
      );

      expect(acceptedResponse.isAccepted, true);
      expect(acceptedResponse.isDeclined, false);
      expect(acceptedResponse.isCounterOffer, false);

      expect(declinedResponse.isAccepted, false);
      expect(declinedResponse.isDeclined, true);
      expect(declinedResponse.isCounterOffer, false);

      expect(counterOfferResponse.isAccepted, false);
      expect(counterOfferResponse.isDeclined, false);
      expect(counterOfferResponse.isCounterOffer, true);
    });
  });

  group('ChallengeStats', () {
    test('should create challenge stats with all fields', () {
      final stats = ChallengeStats(
        totalChallenges: 10,
        openChallenges: 5,
        acceptedChallenges: 3,
        completedChallenges: 2,
        wonChallenges: 1,
        lostChallenges: 1,
        winRate: 0.5,
        disputedChallenges: 0,
      );

      expect(stats.totalChallenges, 10);
      expect(stats.openChallenges, 5);
      expect(stats.acceptedChallenges, 3);
      expect(stats.completedChallenges, 2);
      expect(stats.wonChallenges, 1);
      expect(stats.lostChallenges, 1);
      expect(stats.winRate, 0.5);
      expect(stats.disputedChallenges, 0);
    });

    test('should create challenge stats from JSON', () {
      final json = {
        'totalChallenges': 10,
        'openChallenges': 5,
        'acceptedChallenges': 3,
        'completedChallenges': 2,
        'wonChallenges': 1,
        'lostChallenges': 1,
        'winRate': 0.5,
        'disputedChallenges': 0,
      };

      final stats = ChallengeStats.fromJson(json);

      expect(stats.totalChallenges, 10);
      expect(stats.openChallenges, 5);
      expect(stats.acceptedChallenges, 3);
      expect(stats.completedChallenges, 2);
      expect(stats.wonChallenges, 1);
      expect(stats.lostChallenges, 1);
      expect(stats.winRate, 0.5);
      expect(stats.disputedChallenges, 0);
    });
  });

  group('MatchSuggestion', () {
    test('should create match suggestion with all fields', () {
      final suggestion = MatchSuggestion(
        id: 'suggestion-123',
        type: 'challenge',
        matchType: '5v5',
        location: 'Dhumbarahi Futsal',
        proposedDateTime: DateTime(2024, 1, 15, 18, 0),
        opponentName: 'Jane Smith',
        teamName: 'Real Madrid',
        wagerAmount: 500.0,
        compatibilityScore: 85,
        description: 'Great match opportunity',
      );

      expect(suggestion.id, 'suggestion-123');
      expect(suggestion.type, 'challenge');
      expect(suggestion.matchType, '5v5');
      expect(suggestion.location, 'Dhumbarahi Futsal');
      expect(suggestion.opponentName, 'Jane Smith');
      expect(suggestion.teamName, 'Real Madrid');
      expect(suggestion.wagerAmount, 500.0);
      expect(suggestion.compatibilityScore, 85);
      expect(suggestion.description, 'Great match opportunity');
    });

    test('should create match suggestion from JSON', () {
      final json = {
        'id': 'suggestion-123',
        'type': 'challenge',
        'matchType': '5v5',
        'location': 'Dhumbarahi Futsal',
        'proposedDateTime': '2024-01-15T18:00:00.000Z',
        'opponentName': 'Jane Smith',
        'teamName': 'Real Madrid',
        'wagerAmount': 500.0,
        'compatibilityScore': 85,
        'description': 'Great match opportunity',
      };

      final suggestion = MatchSuggestion.fromJson(json);

      expect(suggestion.id, 'suggestion-123');
      expect(suggestion.type, 'challenge');
      expect(suggestion.matchType, '5v5');
      expect(suggestion.location, 'Dhumbarahi Futsal');
      expect(suggestion.opponentName, 'Jane Smith');
      expect(suggestion.teamName, 'Real Madrid');
      expect(suggestion.wagerAmount, 500.0);
      expect(suggestion.compatibilityScore, 85);
      expect(suggestion.description, 'Great match opportunity');
    });
  });

  group('PagedChallenges', () {
    test('should create paged challenges with all fields', () {
      final challenges = [
        Challenge(
          id: 'challenge-1',
          challengerId: 'user-123',
          matchType: '5v5',
          location: 'Dhumbarahi Futsal',
          proposedDateTime: DateTime(2024, 1, 15, 18, 0),
          expiresAt: DateTime(2024, 1, 16, 18, 0),
          isResultDisputed: false,
          responseCount: 0,
          created: DateTime(2024, 1, 15, 10, 0),
        ),
      ];

      final pagedChallenges = PagedChallenges(
        items: challenges,
        total: 1,
        page: 1,
        pageSize: 20,
        totalPages: 1,
      );

      expect(pagedChallenges.items?.length, 1);
      expect(pagedChallenges.total, 1);
      expect(pagedChallenges.page, 1);
      expect(pagedChallenges.pageSize, 20);
      expect(pagedChallenges.totalPages, 1);
      expect(pagedChallenges.items?.first.id, 'challenge-1');
    });

    test('should create paged challenges from JSON', () {
      final json = {
        'items': [
          {
            'id': 'challenge-1',
            'challengerId': 'user-123',
            'matchType': '5v5',
            'location': 'Dhumbarahi Futsal',
            'proposedDateTime': '2024-01-15T18:00:00.000Z',
            'expiresAt': '2024-01-16T18:00:00.000Z',
            'isResultDisputed': false,
            'responseCount': 0,
            'created': '2024-01-15T10:00:00.000Z',
          }
        ],
        'total': 1,
        'page': 1,
        'pageSize': 20,
        'totalPages': 1,
      };

      final pagedChallenges = PagedChallenges.fromJson(json);

      expect(pagedChallenges.items?.length, 1);
      expect(pagedChallenges.total, 1);
      expect(pagedChallenges.page, 1);
      expect(pagedChallenges.pageSize, 20);
      expect(pagedChallenges.totalPages, 1);
      expect(pagedChallenges.items?.first.id, 'challenge-1');
    });
  });
}
