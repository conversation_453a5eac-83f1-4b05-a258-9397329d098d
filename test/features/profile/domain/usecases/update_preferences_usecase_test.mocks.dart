// Mocks generated by <PERSON>ckito 5.4.6 from annotations
// in nextsportz_v2/test/features/profile/domain/usecases/update_preferences_usecase_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i3;

import 'package:fpdart/fpdart.dart' as _i4;
import 'package:mockito/mockito.dart' as _i1;
import 'package:mockito/src/dummies.dart' as _i6;
import 'package:nextsportz_v2/features/profile/domain/entities/profile.dart'
    as _i5;
import 'package:nextsportz_v2/features/profile/domain/repositories/profile_repository.dart'
    as _i2;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

/// A class which mocks [ProfileRepository].
///
/// See the documentation for Mockito's code generation for more information.
class MockProfileRepository extends _i1.Mock implements _i2.ProfileRepository {
  MockProfileRepository() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i3.Future<_i4.Either<String, _i5.Profile>> getProfile(String? userId) =>
      (super.noSuchMethod(
        Invocation.method(
          #getProfile,
          [userId],
        ),
        returnValue: _i3.Future<_i4.Either<String, _i5.Profile>>.value(
            _i6.dummyValue<_i4.Either<String, _i5.Profile>>(
          this,
          Invocation.method(
            #getProfile,
            [userId],
          ),
        )),
      ) as _i3.Future<_i4.Either<String, _i5.Profile>>);

  @override
  _i3.Future<_i4.Either<String, _i5.Profile>> updateProfile(
          _i5.Profile? profile) =>
      (super.noSuchMethod(
        Invocation.method(
          #updateProfile,
          [profile],
        ),
        returnValue: _i3.Future<_i4.Either<String, _i5.Profile>>.value(
            _i6.dummyValue<_i4.Either<String, _i5.Profile>>(
          this,
          Invocation.method(
            #updateProfile,
            [profile],
          ),
        )),
      ) as _i3.Future<_i4.Either<String, _i5.Profile>>);

  @override
  _i3.Future<_i4.Either<String, void>> updateProfileImage(
    String? userId,
    String? imagePath,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #updateProfileImage,
          [
            userId,
            imagePath,
          ],
        ),
        returnValue: _i3.Future<_i4.Either<String, void>>.value(
            _i6.dummyValue<_i4.Either<String, void>>(
          this,
          Invocation.method(
            #updateProfileImage,
            [
              userId,
              imagePath,
            ],
          ),
        )),
      ) as _i3.Future<_i4.Either<String, void>>);

  @override
  _i3.Future<_i4.Either<String, void>> updatePreferences(
    String? userId,
    Map<String, dynamic>? preferences,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #updatePreferences,
          [
            userId,
            preferences,
          ],
        ),
        returnValue: _i3.Future<_i4.Either<String, void>>.value(
            _i6.dummyValue<_i4.Either<String, void>>(
          this,
          Invocation.method(
            #updatePreferences,
            [
              userId,
              preferences,
            ],
          ),
        )),
      ) as _i3.Future<_i4.Either<String, void>>);

  @override
  _i3.Future<_i4.Either<String, void>> deleteProfile(String? userId) =>
      (super.noSuchMethod(
        Invocation.method(
          #deleteProfile,
          [userId],
        ),
        returnValue: _i3.Future<_i4.Either<String, void>>.value(
            _i6.dummyValue<_i4.Either<String, void>>(
          this,
          Invocation.method(
            #deleteProfile,
            [userId],
          ),
        )),
      ) as _i3.Future<_i4.Either<String, void>>);
}
