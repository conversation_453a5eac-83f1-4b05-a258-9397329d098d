import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:nextsportz_v2/core/networking/api_client.dart';
import 'package:nextsportz_v2/features/teams/data/datasources/teams_remote_datasource.dart';
import 'package:nextsportz_v2/features/teams/data/dto/team_dto.dart';
import 'package:nextsportz_v2/features/teams/data/dto/team_request_dto.dart';
import 'package:nextsportz_v2/features/teams/domain/entities/team.dart';
import 'package:dio/dio.dart';
import 'package:nextsportz_v2/core/networking/exception.dart';

import 'teams_remote_datasource_test.mocks.dart';

@GenerateMocks([ApiClient])
void main() {
  group('TeamsRemoteDatasource', () {
    late MockApiClient mockApiClient;
    late TeamsRemoteDataSource teamsRemoteDatasource;

    setUp(() {
      mockApiClient = MockApiClient();
      teamsRemoteDatasource = TeamsRemoteDataSource(mockApiClient);
    });

    group('getMyTeams', () {
      test('should get teams successfully', () async {
        // Arrange
        final teamsData = [
          {
            'id': '1',
            'name': 'Team Alpha',
            'description': 'A great team',
            'logo': 'logo1.png',
            'slogan': 'We are the best',
            'created_by': 'user1',
            'created_at': '2023-01-01T00:00:00Z',
            'updated_at': '2023-01-01T00:00:00Z',
            'is_active': true,
            'members': [],
            'invitations': [],
            'stats': {
              'total_matches': 10,
              'wins': 7,
              'losses': 2,
              'draws': 1,
              'win_rate': 0.7,
              'total_goals': 25,
              'goals_conceded': 10,
              'clean_sheets': 3,
              'average_goals_per_match': 2.5,
            },
          },
        ];

        when(mockApiClient.get('/api/teams/my-teams'))
            .thenAnswer((_) async => {'data': teamsData});

        // Act
        final result = await teamsRemoteDatasource.getMyTeams();

        // Assert
        expect(result, isA<List<Team>>());
        expect(result.length, equals(1));
        expect(result.first.name, equals('Team Alpha'));
        verify(mockApiClient.get('/api/teams/my-teams')).called(1);
      });

      test('should bubble DioExceptionHandle on transport error', () async {
        final dioError = DioException(
          requestOptions: RequestOptions(path: '/api/teams/my-teams'),
          response: Response(
            requestOptions: RequestOptions(path: '/api/teams/my-teams'),
            statusCode: 500,
          ),
          type: DioExceptionType.badResponse,
          message: 'Server error',
        );

        when(mockApiClient.get('/api/teams/my-teams'))
            .thenThrow(DioExceptionHandle.fromDioError(dioError));

        expect(
          () => teamsRemoteDatasource.getMyTeams(),
          throwsA(isA<DioExceptionHandle>()),
        );
      });
    });

    group('getTeamById', () {
      test('should get team by id successfully', () async {
        // Arrange
        const teamId = '1';
        final teamData = {
          'id': teamId,
          'name': 'Team Alpha',
          'description': 'A great team',
          'logo': 'logo1.png',
          'slogan': 'We are the best',
          'created_by': 'user1',
          'created_at': '2023-01-01T00:00:00Z',
          'updated_at': '2023-01-01T00:00:00Z',
          'is_active': true,
          'members': [],
          'invitations': [],
          'stats': {
            'total_matches': 10,
            'wins': 7,
            'losses': 2,
            'draws': 1,
            'win_rate': 0.7,
            'total_goals': 25,
            'goals_conceded': 10,
            'clean_sheets': 3,
            'average_goals_per_match': 2.5,
          },
        };

        when(mockApiClient.get('/api/teams/$teamId'))
            .thenAnswer((_) async => teamData);

        // Act
        final result = await teamsRemoteDatasource.getTeamById(teamId);

        // Assert
        expect(result, isA<Team>());
        expect(result.id, equals(teamId));
        expect(result.name, equals('Team Alpha'));
        verify(mockApiClient.get('/api/teams/$teamId')).called(1);
      });
    });

    group('createTeam', () {
      test('should create team successfully', () async {
        // Arrange
        final requestDto = CreateTeamRequestDto(
          name: 'New Team',
          description: 'A new team',
          logo: 'new_logo.png',
          slogan: 'New slogan',
        );

        final responseData = {
          'id': '2',
          'name': 'New Team',
          'description': 'A new team',
          'logo': 'new_logo.png',
          'slogan': 'New slogan',
          'created_by': 'user1',
          'created_at': '2023-01-01T00:00:00Z',
          'updated_at': '2023-01-01T00:00:00Z',
          'is_active': true,
          'members': [],
          'invitations': [],
          'stats': {
            'total_matches': 0,
            'wins': 0,
            'losses': 0,
            'draws': 0,
            'win_rate': 0.0,
            'total_goals': 0,
            'goals_conceded': 0,
            'clean_sheets': 0,
            'average_goals_per_match': 0.0,
          },
        };

        when(mockApiClient.post('/api/teams', data: requestDto.toJson()))
            .thenAnswer((_) async => responseData);

        // Act
        final result = await teamsRemoteDatasource.createTeam(
          name: 'New Team',
          description: 'A new team',
          logo: 'new_logo.png',
          slogan: 'New slogan',
        );

        // Assert
        expect(result, isA<Team>());
        expect(result.name, equals('New Team'));
        expect(result.description, equals('A new team'));
        verify(mockApiClient.post('/api/teams', data: requestDto.toJson()))
            .called(1);
      });
    });

    group('updateTeam', () {
      test('should update team successfully', () async {
        // Arrange
        const teamId = '1';
        final requestDto = UpdateTeamRequestDto(
          name: 'Updated Team',
          description: 'Updated description',
          logo: 'updated_logo.png',
          slogan: 'Updated slogan',
        );

        final responseData = {
          'id': teamId,
          'name': 'Updated Team',
          'description': 'Updated description',
          'logo': 'updated_logo.png',
          'slogan': 'Updated slogan',
          'created_by': 'user1',
          'created_at': '2023-01-01T00:00:00Z',
          'updated_at': '2023-01-02T00:00:00Z',
          'is_active': true,
          'members': [],
          'invitations': [],
          'stats': {
            'total_matches': 10,
            'wins': 7,
            'losses': 2,
            'draws': 1,
            'win_rate': 0.7,
            'total_goals': 25,
            'goals_conceded': 10,
            'clean_sheets': 3,
            'average_goals_per_match': 2.5,
          },
        };

        when(mockApiClient.put('/api/teams/$teamId', data: requestDto.toJson()))
            .thenAnswer((_) async => responseData);

        // Act
        final result = await teamsRemoteDatasource.updateTeam(
          teamId: teamId,
          name: 'Updated Team',
          description: 'Updated description',
          logo: 'updated_logo.png',
          slogan: 'Updated slogan',
        );

        // Assert
        expect(result, isA<Team>());
        expect(result.name, equals('Updated Team'));
        expect(result.description, equals('Updated description'));
        verify(mockApiClient.put('/api/teams/$teamId',
                data: requestDto.toJson()))
            .called(1);
      });
    });

    group('deleteTeam', () {
      test('should delete team successfully', () async {
        // Arrange
        const teamId = '1';

        when(mockApiClient.delete('/api/teams/$teamId'))
            .thenAnswer((_) async => {});

        // Act
        await teamsRemoteDatasource.deleteTeam(teamId);

        // Assert
        verify(mockApiClient.delete('/api/teams/$teamId')).called(1);
      });
    });

    group('invitePlayer', () {
      test('should invite player successfully', () async {
        // Arrange
        const teamId = '1';
        const playerId = 'player1';
        const message = 'Join our team!';
        final requestDto = InvitePlayerRequestDto(
          playerId: playerId,
          message: message,
        );

        when(mockApiClient.post('/api/teams/$teamId/invite',
                data: requestDto.toJson()))
            .thenAnswer((_) async => {});

        // Act
        await teamsRemoteDatasource.invitePlayer(
          teamId: teamId,
          playerId: playerId,
          message: message,
        );

        // Assert
        verify(mockApiClient.post('/api/teams/$teamId/invite',
                data: requestDto.toJson()))
            .called(1);
      });
    });

    group('acceptInvitation', () {
      test('should accept invitation successfully', () async {
        // Arrange
        const invitationId = 'inv1';
        final requestDto = InvitationResponseRequestDto(action: 'accept');

        when(mockApiClient.post('/api/teams/invitations/$invitationId/respond',
                data: requestDto.toJson()))
            .thenAnswer((_) async => {});

        // Act
        await teamsRemoteDatasource.acceptInvitation(invitationId);

        // Assert
        verify(mockApiClient.post(
                '/api/teams/invitations/$invitationId/respond',
                data: requestDto.toJson()))
            .called(1);
      });
    });

    group('declineInvitation', () {
      test('should decline invitation successfully', () async {
        // Arrange
        const invitationId = 'inv1';
        final requestDto = InvitationResponseRequestDto(action: 'decline');

        when(mockApiClient.post('/api/teams/invitations/$invitationId/respond',
                data: requestDto.toJson()))
            .thenAnswer((_) async => {});

        // Act
        await teamsRemoteDatasource.declineInvitation(invitationId);

        // Assert
        verify(mockApiClient.post(
                '/api/teams/invitations/$invitationId/respond',
                data: requestDto.toJson()))
            .called(1);
      });
    });

    group('removeMember', () {
      test('should remove member successfully', () async {
        // Arrange
        const teamId = '1';
        const memberId = 'member1';

        when(mockApiClient.delete('/api/teams/$teamId/members/$memberId'))
            .thenAnswer((_) async => {});

        // Act
        await teamsRemoteDatasource.removeMember(
          teamId: teamId,
          memberId: memberId,
        );

        // Assert
        verify(mockApiClient.delete('/api/teams/$teamId/members/$memberId'))
            .called(1);
      });
    });

    group('updateMemberRole', () {
      test('should update member role successfully', () async {
        // Arrange
        const teamId = '1';
        const memberId = 'member1';
        const role = 'CAPTAIN';
        final requestDto = UpdateMemberRoleRequestDto(role: role);

        when(mockApiClient.put('/api/teams/$teamId/members/$memberId/role',
                data: requestDto.toJson()))
            .thenAnswer((_) async => {});

        // Act
        await teamsRemoteDatasource.updateMemberRole(
          teamId: teamId,
          memberId: memberId,
          role: role,
        );

        // Assert
        verify(mockApiClient.put('/api/teams/$teamId/members/$memberId/role',
                data: requestDto.toJson()))
            .called(1);
      });
    });

    group('getPendingInvitations', () {
      test('should get pending invitations successfully', () async {
        // Arrange
        final invitationsData = [
          {
            'id': 'inv1',
            'team_id': '1',
            'invited_user_id': 'user1',
            'invited_user_name': 'John Doe',
            'invited_user_email': '<EMAIL>',
            'invited_user_phone': '1234567890',
            'status': 'PENDING',
            'created_at': '2023-01-01T00:00:00Z',
            'responded_at': null,
          },
        ];

        when(mockApiClient.get('/api/teams/invitations'))
            .thenAnswer((_) async => {'data': invitationsData});

        // Act
        final result = await teamsRemoteDatasource.getPendingInvitations();

        // Assert
        expect(result, isA<List<TeamInvitation>>());
        expect(result.length, equals(1));
        expect(result.first.id, equals('inv1'));
        verify(mockApiClient.get('/api/teams/invitations')).called(1);
      });
    });

    group('uploadTeamLogo', () {
      test('should upload team logo successfully', () async {
        // Arrange
        final mockFile = MockFile();
        final responseData = {'url': 'https://example.com/logo.png'};

        when(mockApiClient.post('/api/upload', data: anyNamed('data')))
            .thenAnswer((_) async => responseData);

        // Act
        final result = await teamsRemoteDatasource.uploadTeamLogo(mockFile);

        // Assert
        expect(result, equals('https://example.com/logo.png'));
        verify(mockApiClient.post('/api/upload', data: anyNamed('data')))
            .called(1);
      });
    });
  });
}

class MockFile {
  String get path => '/path/to/file.jpg';
}
