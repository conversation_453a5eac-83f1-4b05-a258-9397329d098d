import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:nextsportz_v2/features/teams/data/repositories/teams_repository_impl.dart';
import 'package:nextsportz_v2/features/teams/domain/entities/team.dart';
import 'package:nextsportz_v2/features/teams/data/datasources/teams_remote_datasource.dart';
import 'package:nextsportz_v2/features/teams/data/datasources/teams_local_datasource.dart';

import 'teams_repository_impl_test.mocks.dart';

@GenerateMocks([TeamsRemoteDataSource, TeamsLocalDataSource])
void main() {
  group('TeamsRepositoryImpl', () {
    late TeamsRepositoryImpl repository;
    late MockTeamsRemoteDataSource mockRemoteDataSource;
    late MockTeamsLocalDataSource mockLocalDataSource;

    setUp(() {
      mockRemoteDataSource = MockTeamsRemoteDataSource();
      mockLocalDataSource = MockTeamsLocalDataSource();
      repository =
          TeamsRepositoryImpl(mockRemoteDataSource, mockLocalDataSource);

      // Set up mock data
      final testTeams = [
        Team(
          id: '1',
          name: 'Test Team',
          description: 'Test Description',
          createdBy: 'user123',
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
          isActive: true,
          members: [
            TeamMember(
              id: '1',
              userId: 'user123',
              name: 'Test Member',
              position: 'Forward',
              role: 'captain',
              joinedAt: DateTime.now(),
              isActive: true,
              stats: PlayerStats(
                matchesPlayed: 10,
                goals: 5,
                assists: 3,
                cleanSheets: 2,
                rating: 4.5,
                yellowCards: 1,
                redCards: 0,
                minutesPlayed: 900,
              ),
            ),
          ],
          invitations: [],
          stats: TeamStats(
            totalMatches: 10,
            wins: 7,
            losses: 2,
            draws: 1,
            winRate: 0.7,
            totalGoals: 25,
            goalsConceded: 8,
            cleanSheets: 3,
            averageGoalsPerMatch: 2.5,
          ),
        ),
      ];

      final testTeam = testTeams.first;
      final testInvitations = [
        TeamInvitation(
          id: 'inv1',
          teamId: '1',
          invitedUserId: 'user456',
          invitedUserName: 'John Doe',
          invitedUserEmail: '<EMAIL>',
          status: 'pending',
          createdAt: DateTime.now(),
        ),
        TeamInvitation(
          id: 'inv2',
          teamId: '1',
          invitedUserId: 'user789',
          invitedUserName: 'Jane Smith',
          invitedUserPhone: '+1234567890',
          status: 'pending',
          createdAt: DateTime.now(),
        ),
      ];

      // Mock remote data source methods
      when(mockRemoteDataSource.getMyTeams())
          .thenAnswer((_) async => testTeams);
      when(mockRemoteDataSource.getTeamById('1'))
          .thenAnswer((_) async => testTeam);
      when(mockRemoteDataSource.getTeamById('non-existent-id'))
          .thenThrow(Exception('Team not found'));
      when(mockRemoteDataSource.getTeamById('test-team-id'))
          .thenAnswer((_) async => testTeam);
      when(mockRemoteDataSource.createTeam(
        name: anyNamed('name'),
        description: anyNamed('description'),
        logo: anyNamed('logo'),
        slogan: anyNamed('slogan'),
      )).thenAnswer((invocation) async {
        final name = invocation.namedArguments[Symbol('name')] as String;
        final description =
            invocation.namedArguments[Symbol('description')] as String;
        final logo = invocation.namedArguments[Symbol('logo')] as String?;
        final slogan = invocation.namedArguments[Symbol('slogan')] as String?;
        return testTeam.copyWith(
          name: name,
          description: description,
          logo: logo,
          slogan: slogan,
        );
      });
      when(mockRemoteDataSource.updateTeam(
        teamId: anyNamed('teamId'),
        name: anyNamed('name'),
        description: anyNamed('description'),
        logo: anyNamed('logo'),
        slogan: anyNamed('slogan'),
      )).thenAnswer((invocation) async {
        final name = invocation.namedArguments[Symbol('name')] as String?;
        final description =
            invocation.namedArguments[Symbol('description')] as String?;
        final logo = invocation.namedArguments[Symbol('logo')] as String?;
        final slogan = invocation.namedArguments[Symbol('slogan')] as String?;
        return testTeam.copyWith(
          name: name,
          description: description,
          logo: logo,
          slogan: slogan,
        );
      });
      when(mockRemoteDataSource.deleteTeam(any)).thenAnswer((_) async {});
      when(mockRemoteDataSource.invitePlayer(
        teamId: anyNamed('teamId'),
        playerId: anyNamed('playerId'),
        message: anyNamed('message'),
      )).thenAnswer((_) async {});
      when(mockRemoteDataSource.acceptInvitation(any)).thenAnswer((_) async {});
      when(mockRemoteDataSource.declineInvitation(any))
          .thenAnswer((_) async {});
      when(mockRemoteDataSource.removeMember(
        teamId: anyNamed('teamId'),
        memberId: anyNamed('memberId'),
      )).thenAnswer((_) async {});
      when(mockRemoteDataSource.updateMemberRole(
        teamId: anyNamed('teamId'),
        memberId: anyNamed('memberId'),
        role: anyNamed('role'),
      )).thenAnswer((_) async {});
      when(mockRemoteDataSource.getPendingInvitations())
          .thenAnswer((_) async => testInvitations);

      // Mock local data source methods
      when(mockLocalDataSource.saveTeams(any)).thenAnswer((_) async {});
      when(mockLocalDataSource.saveTeam(any)).thenAnswer((_) async {});
      when(mockLocalDataSource.getMyTeams()).thenAnswer((_) async => testTeams);
      when(mockLocalDataSource.getTeamById('1'))
          .thenAnswer((_) async => testTeam);
      when(mockLocalDataSource.getTeamById('test-team-id'))
          .thenAnswer((_) async => testTeam);
      when(mockLocalDataSource.getTeamById('non-existent-id'))
          .thenAnswer((_) async => null);
      when(mockLocalDataSource.deleteTeam(any)).thenAnswer((_) async {});
      when(mockLocalDataSource.saveInvitation(any)).thenAnswer((_) async {});
      when(mockLocalDataSource.deleteInvitation(any)).thenAnswer((_) async {});
      when(mockLocalDataSource.getPendingInvitations())
          .thenAnswer((_) async => testInvitations);
    });

    group('getMyTeams', () {
      test('should return list of teams', () async {
        // act
        final result = await repository.getMyTeams();

        // assert
        expect(result, isA<List<Team>>());
        expect(result.length, greaterThan(0));
        expect(result.first, isA<Team>());
        expect(result.first.name, isNotEmpty);
        expect(result.first.description, isNotEmpty);
        expect(result.first.createdBy, isNotEmpty);
        expect(result.first.isActive, isTrue);
      });

      test('should return teams with members', () async {
        // act
        final result = await repository.getMyTeams();

        // assert
        expect(result.first.members, isA<List<TeamMember>>());
        expect(result.first.members.length, greaterThan(0));
        expect(result.first.members.first, isA<TeamMember>());
        expect(result.first.members.first.name, isNotEmpty);
        expect(result.first.members.first.position, isNotEmpty);
        expect(result.first.members.first.role, isNotEmpty);
      });

      test('should return teams with stats', () async {
        // act
        final result = await repository.getMyTeams();

        // assert
        expect(result.first.stats, isA<TeamStats>());
        expect(result.first.stats.totalMatches, greaterThanOrEqualTo(0));
        expect(result.first.stats.wins, greaterThanOrEqualTo(0));
        expect(result.first.stats.losses, greaterThanOrEqualTo(0));
        expect(result.first.stats.draws, greaterThanOrEqualTo(0));
        expect(result.first.stats.winRate, greaterThanOrEqualTo(0.0));
      });
    });

    group('getTeamById', () {
      test('should return team by id', () async {
        // arrange
        final teams = await repository.getMyTeams();
        final teamId = teams.first.id;

        // act
        final result = await repository.getTeamById(teamId);

        // assert
        expect(result, isA<Team>());
        expect(result.id, teamId);
        expect(result.name, isNotEmpty);
      });

      test('should throw exception for non-existent team', () async {
        // act & assert
        expect(
          () => repository.getTeamById('non-existent-id'),
          throwsA(isA<Exception>()),
        );
      });
    });

    group('createTeam', () {
      test('should create a new team', () async {
        // arrange
        const name = 'New Test Team';
        const description = 'A new test team description';

        // act
        final result = await repository.createTeam(
          name: name,
          description: description,
        );

        // assert
        expect(result, isA<Team>());
        expect(result.name, name);
        expect(result.description, description);
        expect(result.createdBy, 'user123');
        expect(result.isActive, isTrue);
        expect(result.members.length, 1);
        expect(result.members.first.role, 'captain');
        expect(result.invitations, isEmpty);
      });

      test('should create team with logo', () async {
        // arrange
        const name = 'Team with Logo';
        const description = 'A team with logo';
        const logo = 'https://example.com/logo.png';

        // act
        final result = await repository.createTeam(
          name: name,
          description: description,
          logo: logo,
        );

        // assert
        expect(result, isA<Team>());
        expect(result.name, name);
        expect(result.description, description);
        expect(result.logo, logo);
      });
    });

    group('updateTeam', () {
      test('should update team details', () async {
        // arrange
        final teams = await repository.getMyTeams();
        final teamId = teams.first.id;
        const newName = 'Updated Team Name';
        const newDescription = 'Updated description';

        // act
        final result = await repository.updateTeam(
          teamId: teamId,
          name: newName,
          description: newDescription,
        );

        // assert
        expect(result, isA<Team>());
        expect(result.id, teamId);
        expect(result.name, newName);
        expect(result.description, newDescription);
      });
    });

    group('deleteTeam', () {
      test('should delete team without throwing exception', () async {
        // arrange
        const teamId = 'test-team-id';

        // act & assert
        expect(() => repository.deleteTeam(teamId), returnsNormally);
      });
    });

    group('invitePlayer', () {
      test('should invite player without throwing exception', () async {
        // arrange
        const teamId = 'test-team-id';
        const playerId = 'test-player-id';
        const message = 'Join our team!';

        // act & assert
        expect(
          () => repository.invitePlayer(
            teamId: teamId,
            playerId: playerId,
            message: message,
          ),
          returnsNormally,
        );
      });
    });

    group('acceptInvitation', () {
      test('should accept invitation without throwing exception', () async {
        // arrange
        const invitationId = 'test-invitation-id';

        // act & assert
        expect(
          () => repository.acceptInvitation(invitationId),
          returnsNormally,
        );
      });
    });

    group('declineInvitation', () {
      test('should decline invitation without throwing exception', () async {
        // arrange
        const invitationId = 'test-invitation-id';

        // act & assert
        expect(
          () => repository.declineInvitation(invitationId),
          returnsNormally,
        );
      });
    });

    group('removeMember', () {
      test('should remove member without throwing exception', () async {
        // arrange
        const teamId = 'test-team-id';
        const memberId = 'test-member-id';

        // act & assert
        expect(
          () => repository.removeMember(teamId: teamId, memberId: memberId),
          returnsNormally,
        );
      });
    });

    group('updateMemberRole', () {
      test('should update member role without throwing exception', () async {
        // arrange
        const teamId = 'test-team-id';
        const memberId = 'test-member-id';
        const newRole = 'vice_captain';

        // act & assert
        expect(
          () => repository.updateMemberRole(
            teamId: teamId,
            memberId: memberId,
            role: newRole,
          ),
          returnsNormally,
        );
      });
    });

    group('getPendingInvitations', () {
      test('should return list of pending invitations', () async {
        // act
        final result = await repository.getPendingInvitations();

        // assert
        expect(result, isA<List<TeamInvitation>>());
        expect(result.length, greaterThan(0));
        expect(result.first, isA<TeamInvitation>());
        expect(result.first.status, 'pending');
        expect(result.first.invitedUserName, isNotEmpty);
        expect(result.first.teamId, isNotEmpty);
      });
    });
  });
}
