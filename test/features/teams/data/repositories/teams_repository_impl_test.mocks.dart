// Mocks generated by Mocki<PERSON> 5.4.6 from annotations
// in nextsportz_v2/test/features/teams/data/repositories/teams_repository_impl_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i5;

import 'package:mockito/mockito.dart' as _i1;
import 'package:mockito/src/dummies.dart' as _i6;
import 'package:nextsportz_v2/core/networking/api_client.dart' as _i2;
import 'package:nextsportz_v2/features/teams/data/datasources/teams_local_datasource.dart'
    as _i7;
import 'package:nextsportz_v2/features/teams/data/datasources/teams_remote_datasource.dart'
    as _i4;
import 'package:nextsportz_v2/features/teams/domain/entities/team.dart' as _i3;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeApiClient_0 extends _i1.SmartFake implements _i2.ApiClient {
  _FakeApiClient_0(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeTeam_1 extends _i1.SmartFake implements _i3.Team {
  _FakeTeam_1(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

/// A class which mocks [TeamsRemoteDataSource].
///
/// See the documentation for Mockito's code generation for more information.
class MockTeamsRemoteDataSource extends _i1.Mock
    implements _i4.TeamsRemoteDataSource {
  MockTeamsRemoteDataSource() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i2.ApiClient get apiClient => (super.noSuchMethod(
        Invocation.getter(#apiClient),
        returnValue: _FakeApiClient_0(
          this,
          Invocation.getter(#apiClient),
        ),
      ) as _i2.ApiClient);

  @override
  _i5.Future<List<_i3.Team>> getMyTeams() => (super.noSuchMethod(
        Invocation.method(
          #getMyTeams,
          [],
        ),
        returnValue: _i5.Future<List<_i3.Team>>.value(<_i3.Team>[]),
      ) as _i5.Future<List<_i3.Team>>);

  @override
  _i5.Future<_i3.Team> getTeamById(String? teamId) => (super.noSuchMethod(
        Invocation.method(
          #getTeamById,
          [teamId],
        ),
        returnValue: _i5.Future<_i3.Team>.value(_FakeTeam_1(
          this,
          Invocation.method(
            #getTeamById,
            [teamId],
          ),
        )),
      ) as _i5.Future<_i3.Team>);

  @override
  _i5.Future<_i3.Team> createTeam({
    required String? name,
    required String? description,
    String? logo,
    String? slogan,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #createTeam,
          [],
          {
            #name: name,
            #description: description,
            #logo: logo,
            #slogan: slogan,
          },
        ),
        returnValue: _i5.Future<_i3.Team>.value(_FakeTeam_1(
          this,
          Invocation.method(
            #createTeam,
            [],
            {
              #name: name,
              #description: description,
              #logo: logo,
              #slogan: slogan,
            },
          ),
        )),
      ) as _i5.Future<_i3.Team>);

  @override
  _i5.Future<_i3.Team> updateTeam({
    required String? teamId,
    String? name,
    String? description,
    String? logo,
    String? slogan,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #updateTeam,
          [],
          {
            #teamId: teamId,
            #name: name,
            #description: description,
            #logo: logo,
            #slogan: slogan,
          },
        ),
        returnValue: _i5.Future<_i3.Team>.value(_FakeTeam_1(
          this,
          Invocation.method(
            #updateTeam,
            [],
            {
              #teamId: teamId,
              #name: name,
              #description: description,
              #logo: logo,
              #slogan: slogan,
            },
          ),
        )),
      ) as _i5.Future<_i3.Team>);

  @override
  _i5.Future<void> deleteTeam(String? teamId) => (super.noSuchMethod(
        Invocation.method(
          #deleteTeam,
          [teamId],
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);

  @override
  _i5.Future<void> invitePlayer({
    required String? teamId,
    required String? playerId,
    String? message,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #invitePlayer,
          [],
          {
            #teamId: teamId,
            #playerId: playerId,
            #message: message,
          },
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);

  @override
  _i5.Future<void> acceptInvitation(String? invitationId) =>
      (super.noSuchMethod(
        Invocation.method(
          #acceptInvitation,
          [invitationId],
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);

  @override
  _i5.Future<void> declineInvitation(String? invitationId) =>
      (super.noSuchMethod(
        Invocation.method(
          #declineInvitation,
          [invitationId],
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);

  @override
  _i5.Future<void> removeMember({
    required String? teamId,
    required String? memberId,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #removeMember,
          [],
          {
            #teamId: teamId,
            #memberId: memberId,
          },
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);

  @override
  _i5.Future<void> updateMemberRole({
    required String? teamId,
    required String? memberId,
    required String? role,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #updateMemberRole,
          [],
          {
            #teamId: teamId,
            #memberId: memberId,
            #role: role,
          },
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);

  @override
  _i5.Future<List<_i3.TeamInvitation>> getPendingInvitations() =>
      (super.noSuchMethod(
        Invocation.method(
          #getPendingInvitations,
          [],
        ),
        returnValue:
            _i5.Future<List<_i3.TeamInvitation>>.value(<_i3.TeamInvitation>[]),
      ) as _i5.Future<List<_i3.TeamInvitation>>);

  @override
  _i5.Future<String> uploadTeamLogo(dynamic imageFile) => (super.noSuchMethod(
        Invocation.method(
          #uploadTeamLogo,
          [imageFile],
        ),
        returnValue: _i5.Future<String>.value(_i6.dummyValue<String>(
          this,
          Invocation.method(
            #uploadTeamLogo,
            [imageFile],
          ),
        )),
      ) as _i5.Future<String>);
}

/// A class which mocks [TeamsLocalDataSource].
///
/// See the documentation for Mockito's code generation for more information.
class MockTeamsLocalDataSource extends _i1.Mock
    implements _i7.TeamsLocalDataSource {
  MockTeamsLocalDataSource() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i5.Future<List<_i3.Team>> getMyTeams() => (super.noSuchMethod(
        Invocation.method(
          #getMyTeams,
          [],
        ),
        returnValue: _i5.Future<List<_i3.Team>>.value(<_i3.Team>[]),
      ) as _i5.Future<List<_i3.Team>>);

  @override
  _i5.Future<_i3.Team?> getTeamById(String? teamId) => (super.noSuchMethod(
        Invocation.method(
          #getTeamById,
          [teamId],
        ),
        returnValue: _i5.Future<_i3.Team?>.value(),
      ) as _i5.Future<_i3.Team?>);

  @override
  _i5.Future<void> saveTeam(_i3.Team? team) => (super.noSuchMethod(
        Invocation.method(
          #saveTeam,
          [team],
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);

  @override
  _i5.Future<void> saveTeams(List<_i3.Team>? teams) => (super.noSuchMethod(
        Invocation.method(
          #saveTeams,
          [teams],
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);

  @override
  _i5.Future<void> deleteTeam(String? teamId) => (super.noSuchMethod(
        Invocation.method(
          #deleteTeam,
          [teamId],
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);

  @override
  _i5.Future<List<_i3.TeamInvitation>> getPendingInvitations() =>
      (super.noSuchMethod(
        Invocation.method(
          #getPendingInvitations,
          [],
        ),
        returnValue:
            _i5.Future<List<_i3.TeamInvitation>>.value(<_i3.TeamInvitation>[]),
      ) as _i5.Future<List<_i3.TeamInvitation>>);

  @override
  _i5.Future<void> saveInvitation(_i3.TeamInvitation? invitation) =>
      (super.noSuchMethod(
        Invocation.method(
          #saveInvitation,
          [invitation],
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);

  @override
  _i5.Future<void> deleteInvitation(String? invitationId) =>
      (super.noSuchMethod(
        Invocation.method(
          #deleteInvitation,
          [invitationId],
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);
}
