import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';

import '../../../../../lib/features/teams/domain/usecases/teams_usecases.dart';
import '../../../../../lib/features/teams/domain/repositories/teams_repository.dart';
import '../../../../../lib/features/teams/domain/entities/team.dart';

import 'create_team_usecase_test.mocks.dart';

@GenerateMocks([TeamsRepository])
void main() {
  group('CreateTeamUseCase Tests', () {
    late CreateTeamUseCase useCase;
    late MockTeamsRepository mockRepository;
    late Team testTeam;

    setUp(() {
      mockRepository = MockTeamsRepository();
      useCase = CreateTeamUseCase(mockRepository);

      testTeam = Team(
        id: '1',
        name: 'Test Team',
        description: 'A test team for unit testing',
        slogan: 'Test to win',
        logo: 'https://example.com/logo.jpg',
        createdBy: 'owner123',
        createdAt: DateTime(2023, 1, 1),
        updatedAt: DateTime(2023, 1, 1),
        isActive: true,
        members: [],
        invitations: [],
        stats: TeamStats(
          totalMatches: 0,
          wins: 0,
          losses: 0,
          draws: 0,
          winRate: 0.0,
          totalGoals: 0,
          goalsConceded: 0,
          cleanSheets: 0,
          averageGoalsPerMatch: 0.0,
        ),
      );
    });

    group('call method', () {
      test('creates team successfully with all parameters', () async {
        // Arrange
        const name = 'Test Team';
        const description = 'A test team for unit testing';
        const slogan = 'Test to win';
        const logo = 'https://example.com/logo.jpg';

        when(mockRepository.createTeam(
          name: name,
          description: description,
          slogan: slogan,
          logo: logo,
        )).thenAnswer((_) async => testTeam);

        // Act
        final result = await useCase.call(
          name: name,
          description: description,
          slogan: slogan,
          logo: logo,
        );

        // Assert
        expect(result.id, testTeam.id);
        expect(result.name, name);
        expect(result.description, description);
        expect(result.slogan, slogan);
        expect(result.logo, logo);
        verify(mockRepository.createTeam(
          name: name,
          description: description,
          slogan: slogan,
          logo: logo,
        )).called(1);
      });

      test('creates team successfully with only required parameters', () async {
        // Arrange
        const name = 'Minimal Team';
        const description = 'Minimal team description';
        final minimalTeam = testTeam.copyWith(
          name: name,
          description: description,
          slogan: null,
          logo: null,
        );

        when(mockRepository.createTeam(
          name: name,
          description: description,
          slogan: null,
          logo: null,
        )).thenAnswer((_) async => minimalTeam);

        // Act
        final result = await useCase.call(
          name: name,
          description: description,
        );

        // Assert
        expect(result.name, name);
        expect(result.description, description);
        expect(result.slogan, null);
        expect(result.logo, null);
        verify(mockRepository.createTeam(
          name: name,
          description: description,
          slogan: null,
          logo: null,
        )).called(1);
      });

      test('creates team with slogan but no logo', () async {
        // Arrange
        const name = 'Team with Slogan';
        const description = 'Team description';
        const slogan = 'Victory is ours';
        final teamWithSlogan = testTeam.copyWith(
          name: name,
          description: description,
          slogan: slogan,
          logo: null,
        );

        when(mockRepository.createTeam(
          name: name,
          description: description,
          slogan: slogan,
          logo: null,
        )).thenAnswer((_) async => teamWithSlogan);

        // Act
        final result = await useCase.call(
          name: name,
          description: description,
          slogan: slogan,
        );

        // Assert
        expect(result.name, name);
        expect(result.slogan, slogan);
        expect(result.logo, null);
        verify(mockRepository.createTeam(
          name: name,
          description: description,
          slogan: slogan,
          logo: null,
        )).called(1);
      });

      test('creates team with logo but no slogan', () async {
        // Arrange
        const name = 'Team with Logo';
        const description = 'Team description';
        const logo = 'https://example.com/team-logo.jpg';
        final teamWithLogo = testTeam.copyWith(
          name: name,
          description: description,
          slogan: null,
          logo: logo,
        );

        when(mockRepository.createTeam(
          name: name,
          description: description,
          slogan: null,
          logo: logo,
        )).thenAnswer((_) async => teamWithLogo);

        // Act
        final result = await useCase.call(
          name: name,
          description: description,
          logo: logo,
        );

        // Assert
        expect(result.name, name);
        expect(result.logo, logo);
        expect(result.slogan, null);
        verify(mockRepository.createTeam(
          name: name,
          description: description,
          slogan: null,
          logo: logo,
        )).called(1);
      });

      test('calls repository with exact parameters provided', () async {
        // Arrange
        const name = 'Exact Team';
        const description = 'Exact description';
        const slogan = 'Exact slogan';
        const logo = 'https://exact.com/logo.png';

        when(mockRepository.createTeam(
          name: name,
          description: description,
          slogan: slogan,
          logo: logo,
        )).thenAnswer((_) async => testTeam);

        // Act
        await useCase.call(
          name: name,
          description: description,
          slogan: slogan,
          logo: logo,
        );

        // Assert
        verify(mockRepository.createTeam(
          name: name,
          description: description,
          slogan: slogan,
          logo: logo,
        )).called(1);
        verifyNoMoreInteractions(mockRepository);
      });

      test('returns team with correct properties from repository', () async {
        // Arrange
        const name = 'Property Team';
        const description = 'Property description';
        final propertyTeam = Team(
          id: 'prop123',
          name: name,
          description: description,
          slogan: 'Props for all',
          logo: 'https://props.com/logo.gif',
          createdBy: 'propOwner',
          members: [],
          createdAt: DateTime(2023, 5, 15),
          isActive: true,
        );

        when(mockRepository.createTeam(
          name: anyNamed('name'),
          description: anyNamed('description'),
          slogan: anyNamed('slogan'),
          logo: anyNamed('logo'),
        )).thenAnswer((_) async => propertyTeam);

        // Act
        final result = await useCase.call(
          name: name,
          description: description,
        );

        // Assert
        expect(result.id, 'prop123');
        expect(result.name, name);
        expect(result.description, description);
        expect(result.slogan, 'Props for all');
        expect(result.logo, 'https://props.com/logo.gif');
        expect(result.createdBy, 'propOwner');
        expect(result.members, isEmpty);
        expect(result.createdAt, DateTime(2023, 5, 15));
        expect(result.isActive, true);
      });
    });

    group('Edge Cases', () {
      test('handles empty string values', () async {
        // Arrange
        const name = '';
        const description = '';
        const slogan = '';
        const logo = '';
        final emptyTeam = testTeam.copyWith(
          name: name,
          description: description,
          slogan: slogan,
          logo: logo,
        );

        when(mockRepository.createTeam(
          name: name,
          description: description,
          slogan: slogan,
          logo: logo,
        )).thenAnswer((_) async => emptyTeam);

        // Act
        final result = await useCase.call(
          name: name,
          description: description,
          slogan: slogan,
          logo: logo,
        );

        // Assert
        expect(result.name, '');
        expect(result.description, '');
        expect(result.slogan, '');
        expect(result.logo, '');
      });

      test('handles very long strings', () async {
        // Arrange
        final longName = 'Team ${'A' * 500}';
        final longDescription = 'Description ${'B' * 1000}';
        final longSlogan = 'Slogan ${'C' * 200}';
        final longLogo =
            'https://example.com/very/long/path/${'logo' * 50}.jpg';

        when(mockRepository.createTeam(
          name: longName,
          description: longDescription,
          slogan: longSlogan,
          logo: longLogo,
        )).thenAnswer((_) async => testTeam);

        // Act
        final result = await useCase.call(
          name: longName,
          description: longDescription,
          slogan: longSlogan,
          logo: longLogo,
        );

        // Assert
        expect(result, testTeam);
        verify(mockRepository.createTeam(
          name: longName,
          description: longDescription,
          slogan: longSlogan,
          logo: longLogo,
        )).called(1);
      });

      test('handles special characters and unicode', () async {
        // Arrange
        const name = 'Team 🚀⚽';
        const description = 'Description with émojis and spëcial chars!@#\$%';
        const slogan = 'Victòry & Hòpe 💪';
        const logo = 'https://example.com/tëam-lògo.jpg';

        when(mockRepository.createTeam(
          name: name,
          description: description,
          slogan: slogan,
          logo: logo,
        )).thenAnswer((_) async => testTeam);

        // Act
        final result = await useCase.call(
          name: name,
          description: description,
          slogan: slogan,
          logo: logo,
        );

        // Assert
        expect(result, testTeam);
        verify(mockRepository.createTeam(
          name: name,
          description: description,
          slogan: slogan,
          logo: logo,
        )).called(1);
      });

      test('handles mixed null and non-null optional parameters', () async {
        // Arrange
        const name = 'Mixed Team';
        const description = 'Mixed description';
        const slogan = 'Mixed slogan';

        when(mockRepository.createTeam(
          name: name,
          description: description,
          slogan: slogan,
          logo: null,
        )).thenAnswer((_) async => testTeam);

        // Act
        final result = await useCase.call(
          name: name,
          description: description,
          slogan: slogan,
          logo: null,
        );

        // Assert
        expect(result, testTeam);
        verify(mockRepository.createTeam(
          name: name,
          description: description,
          slogan: slogan,
          logo: null,
        )).called(1);
      });

      test('handles different URL formats for logo', () async {
        // Arrange
        final logoUrls = [
          'https://example.com/logo.jpg',
          'http://example.com/logo.png',
          'file:///local/path/logo.gif',
          'data:image/jpeg;base64,/9j/4AAQSkZJRgABAQEAYABgAAD',
          '../relative/path/logo.svg',
        ];

        when(mockRepository.createTeam(
          name: anyNamed('name'),
          description: anyNamed('description'),
          slogan: anyNamed('slogan'),
          logo: anyNamed('logo'),
        )).thenAnswer((_) async => testTeam);

        // Act & Assert
        for (final logoUrl in logoUrls) {
          final result = await useCase.call(
            name: 'Test Team',
            description: 'Test description',
            logo: logoUrl,
          );
          expect(result, testTeam);
        }

        // Verify all calls were made
        for (final logoUrl in logoUrls) {
          verify(mockRepository.createTeam(
            name: 'Test Team',
            description: 'Test description',
            slogan: null,
            logo: logoUrl,
          )).called(1);
        }
      });
    });

    group('Error Handling', () {
      test('propagates repository exceptions', () async {
        // Arrange
        const name = 'Error Team';
        const description = 'Error description';
        when(mockRepository.createTeam(
          name: name,
          description: description,
          slogan: null,
          logo: null,
        )).thenThrow(Exception('Repository error'));

        // Act & Assert
        expect(
          () => useCase.call(name: name, description: description),
          throwsA(isA<Exception>()),
        );
      });

      test('propagates custom exceptions', () async {
        // Arrange
        const name = 'Custom Error Team';
        const description = 'Custom error description';
        when(mockRepository.createTeam(
          name: name,
          description: description,
          slogan: null,
          logo: null,
        )).thenThrow(ArgumentError('Invalid team name'));

        // Act & Assert
        expect(
          () => useCase.call(name: name, description: description),
          throwsA(isA<ArgumentError>()),
        );
      });

      test('propagates string errors', () async {
        // Arrange
        const name = 'String Error Team';
        const description = 'String error description';
        when(mockRepository.createTeam(
          name: name,
          description: description,
          slogan: null,
          logo: null,
        )).thenThrow('String error message');

        // Act & Assert
        expect(
          () => useCase.call(name: name, description: description),
          throwsA(equals('String error message')),
        );
      });
    });

    group('Performance Tests', () {
      test('handles multiple sequential team creations', () async {
        // Arrange
        final teamData = [
          {'name': 'Team 1', 'description': 'Description 1'},
          {'name': 'Team 2', 'description': 'Description 2'},
          {'name': 'Team 3', 'description': 'Description 3'},
        ];

        when(mockRepository.createTeam(
          name: anyNamed('name'),
          description: anyNamed('description'),
          slogan: anyNamed('slogan'),
          logo: anyNamed('logo'),
        )).thenAnswer((_) async => testTeam);

        // Act
        for (final data in teamData) {
          final result = await useCase.call(
            name: data['name']!,
            description: data['description']!,
          );
          expect(result, testTeam);
        }

        // Assert
        for (final data in teamData) {
          verify(mockRepository.createTeam(
            name: data['name']!,
            description: data['description']!,
            slogan: null,
            logo: null,
          )).called(1);
        }
      });

      test('handles concurrent team creations', () async {
        // Arrange
        final teamData = [
          {
            'name': 'Concurrent Team 1',
            'description': 'Concurrent Description 1'
          },
          {
            'name': 'Concurrent Team 2',
            'description': 'Concurrent Description 2'
          },
          {
            'name': 'Concurrent Team 3',
            'description': 'Concurrent Description 3'
          },
        ];

        when(mockRepository.createTeam(
          name: anyNamed('name'),
          description: anyNamed('description'),
          slogan: anyNamed('slogan'),
          logo: anyNamed('logo'),
        )).thenAnswer((_) async {
          await Future.delayed(const Duration(milliseconds: 50));
          return testTeam;
        });

        // Act
        final futures = teamData.map((data) => useCase.call(
              name: data['name']!,
              description: data['description']!,
            ));
        final results = await Future.wait(futures);

        // Assert
        for (final result in results) {
          expect(result, testTeam);
        }
        expect(results.length, teamData.length);
      });

      test('handles large team data efficiently', () async {
        // Arrange
        final largeDescription = 'Large description ${'data ' * 1000}';
        const name = 'Large Data Team';

        when(mockRepository.createTeam(
          name: name,
          description: largeDescription,
          slogan: null,
          logo: null,
        )).thenAnswer((_) async => testTeam);

        // Act
        final stopwatch = Stopwatch()..start();
        final result = await useCase.call(
          name: name,
          description: largeDescription,
        );
        stopwatch.stop();

        // Assert
        expect(result, testTeam);
        // Performance assertion - should complete within reasonable time
        expect(stopwatch.elapsedMilliseconds, lessThan(5000));
      });
    });

    group('Input Validation', () {
      test('passes through all parameter combinations correctly', () async {
        // Arrange
        final testCases = [
          {
            'name': 'Team A',
            'description': 'Description A',
            'slogan': null,
            'logo': null,
          },
          {
            'name': 'Team B',
            'description': 'Description B',
            'slogan': 'Slogan B',
            'logo': null,
          },
          {
            'name': 'Team C',
            'description': 'Description C',
            'slogan': null,
            'logo': 'logo-c.jpg',
          },
          {
            'name': 'Team D',
            'description': 'Description D',
            'slogan': 'Slogan D',
            'logo': 'logo-d.jpg',
          },
        ];

        when(mockRepository.createTeam(
          name: anyNamed('name'),
          description: anyNamed('description'),
          slogan: anyNamed('slogan'),
          logo: anyNamed('logo'),
        )).thenAnswer((_) async => testTeam);

        // Act & Assert
        for (final testCase in testCases) {
          final result = await useCase.call(
            name: testCase['name'] as String,
            description: testCase['description'] as String,
            slogan: testCase['slogan'] as String?,
            logo: testCase['logo'] as String?,
          );

          expect(result, testTeam);
          verify(mockRepository.createTeam(
            name: testCase['name'] as String,
            description: testCase['description'] as String,
            slogan: testCase['slogan'] as String?,
            logo: testCase['logo'] as String?,
          )).called(1);
        }
      });
    });
  });
}
