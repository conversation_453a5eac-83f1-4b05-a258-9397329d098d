import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:nextsportz_v2/features/teams/domain/entities/team.dart';
import 'package:nextsportz_v2/features/teams/domain/repositories/teams_repository.dart';
import 'package:nextsportz_v2/features/teams/domain/usecases/teams_usecases.dart';

import 'teams_usecases_test.mocks.dart';

@GenerateMocks([TeamsRepository])
void main() {
  late MockTeamsRepository mockRepository;
  late GetMyTeamsUseCase getMyTeamsUseCase;
  late CreateTeamUseCase createTeamUseCase;
  late GetTeamByIdUseCase getTeamByIdUseCase;
  late InvitePlayerUseCase invitePlayerUseCase;

  setUp(() {
    mockRepository = MockTeamsRepository();
    getMyTeamsUseCase = GetMyTeamsUseCase(mockRepository);
    createTeamUseCase = CreateTeamUseCase(mockRepository);
    getTeamByIdUseCase = GetTeamByIdUseCase(mockRepository);
    invitePlayerUseCase = InvitePlayerUseCase(mockRepository);
  });

  group('GetMyTeamsUseCase', () {
    test('should get list of teams from repository', () async {
      // arrange
      final teams = [
        Team(
          id: '1',
          name: 'Team 1',
          description: 'Description 1',
          createdBy: 'user1',
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
          isActive: true,
          members: [],
          invitations: [],
          stats: const TeamStats(
            totalMatches: 0,
            wins: 0,
            losses: 0,
            draws: 0,
            winRate: 0.0,
            totalGoals: 0,
            goalsConceded: 0,
            cleanSheets: 0,
            averageGoalsPerMatch: 0.0,
          ),
        ),
      ];

      when(mockRepository.getMyTeams()).thenAnswer((_) async => teams);

      // act
      final result = await getMyTeamsUseCase();

      // assert
      expect(result, teams);
      verify(mockRepository.getMyTeams()).called(1);
    });
  });

  group('CreateTeamUseCase', () {
    test('should create a team with given parameters', () async {
      // arrange
      const name = 'New Team';
      const description = 'A new team';
      const logo = 'logo_url';

      final createdTeam = Team(
        id: '1',
        name: name,
        description: description,
        logo: logo,
        createdBy: 'user1',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        isActive: true,
        members: [],
        invitations: [],
        stats: const TeamStats(
          totalMatches: 0,
          wins: 0,
          losses: 0,
          draws: 0,
          winRate: 0.0,
          totalGoals: 0,
          goalsConceded: 0,
          cleanSheets: 0,
          averageGoalsPerMatch: 0.0,
        ),
      );

      when(
        mockRepository.createTeam(
          name: name,
          description: description,
          logo: logo,
        ),
      ).thenAnswer((_) async => createdTeam);

      // act
      final result = await createTeamUseCase(
        name: name,
        description: description,
        logo: logo,
      );

      // assert
      expect(result, createdTeam);
      verify(
        mockRepository.createTeam(
          name: name,
          description: description,
          logo: logo,
        ),
      ).called(1);
    });
  });

  group('GetTeamByIdUseCase', () {
    test('should get team by id from repository', () async {
      // arrange
      const teamId = '1';
      final team = Team(
        id: teamId,
        name: 'Team 1',
        description: 'Description 1',
        createdBy: 'user1',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        isActive: true,
        members: [],
        invitations: [],
        stats: const TeamStats(
          totalMatches: 0,
          wins: 0,
          losses: 0,
          draws: 0,
          winRate: 0.0,
          totalGoals: 0,
          goalsConceded: 0,
          cleanSheets: 0,
          averageGoalsPerMatch: 0.0,
        ),
      );

      when(mockRepository.getTeamById(teamId)).thenAnswer((_) async => team);

      // act
      final result = await getTeamByIdUseCase(teamId);

      // assert
      expect(result, team);
      verify(mockRepository.getTeamById(teamId)).called(1);
    });
  });

  group('InvitePlayerUseCase', () {
    test('should invite player to team', () async {
      // arrange
      const teamId = '1';
      const playerId = 'user2';
      const message = 'Join our team!';

      when(
        mockRepository.invitePlayer(
          teamId: teamId,
          playerId: playerId,
          message: message,
        ),
      ).thenAnswer((_) async {});

      // act
      await invitePlayerUseCase(
        teamId: teamId,
        playerId: playerId,
        message: message,
      );

      // assert
      verify(
        mockRepository.invitePlayer(
          teamId: teamId,
          playerId: playerId,
          message: message,
        ),
      ).called(1);
    });

    test('should invite player without message', () async {
      // arrange
      const teamId = '1';
      const playerId = 'user2';

      when(
        mockRepository.invitePlayer(
          teamId: teamId,
          playerId: playerId,
          message: null,
        ),
      ).thenAnswer((_) async {});

      // act
      await invitePlayerUseCase(
        teamId: teamId,
        playerId: playerId,
        message: null,
      );

      // assert
      verify(
        mockRepository.invitePlayer(
          teamId: teamId,
          playerId: playerId,
          message: null,
        ),
      ).called(1);
    });
  });
}
