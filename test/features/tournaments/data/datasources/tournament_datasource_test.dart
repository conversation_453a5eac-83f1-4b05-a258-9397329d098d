import 'package:flutter_test/flutter_test.dart';
import 'package:nextsportz_v2/features/tournaments/data/datasources/tournament_datasource.dart';
import 'package:nextsportz_v2/features/tournaments/domain/entities/tournament.dart';
import 'package:nextsportz_v2/features/tournaments/domain/entities/tournament_filter.dart';

void main() {
  group('TournamentDataSourceImpl Tests', () {
    late TournamentDataSourceImpl dataSource;

    setUp(() {
      dataSource = TournamentDataSourceImpl();
    });

    test('should return list of tournaments', () async {
      final tournaments = await dataSource.getTournaments();

      expect(tournaments, isA<List<Tournament>>());
      expect(tournaments.length, greaterThan(0));
    });

    test('should return tournament by ID', () async {
      final tournaments = await dataSource.getTournaments();
      final firstTournament = tournaments.first;

      final tournament = await dataSource.getTournamentById(firstTournament.id);

      expect(tournament, isNotNull);
      expect(tournament!.id, firstTournament.id);
    });

    test('should return null for non-existent tournament ID', () async {
      final tournament = await dataSource.getTournamentById('non_existent_id');

      expect(tournament, isNull);
    });

    test('should join tournament successfully', () async {
      final tournaments = await dataSource.getTournaments();
      final tournamentToJoin = tournaments.firstWhere(
        (t) => t.hasAvailableSlots && t.isRegistrationOpen,
      );

      final initialParticipants = tournamentToJoin.currentParticipants;

      final updatedTournament = await dataSource.joinTournament(
        tournamentToJoin.id,
        'test_player_id',
      );

      expect(updatedTournament.currentParticipants, initialParticipants + 1);
      expect(
        updatedTournament.participants.any(
          (p) => p.playerId == 'test_player_id',
        ),
        true,
      );
    });

    test('should throw error when joining full tournament', () async {
      final tournaments = await dataSource.getTournaments();
      final fullTournament = tournaments.firstWhere(
        (t) => !t.hasAvailableSlots,
        orElse:
            () => tournaments.first.copyWith(
              currentParticipants: tournaments.first.maxParticipants,
            ),
      );

      expect(
        () async => await dataSource.joinTournament(
          fullTournament.id,
          'test_player_id',
        ),
        throwsException,
      );
    });

    test('should leave tournament successfully', () async {
      // First join a tournament
      final tournaments = await dataSource.getTournaments();
      final tournament = tournaments.firstWhere(
        (t) => t.hasAvailableSlots && t.isRegistrationOpen,
      );

      await dataSource.joinTournament(tournament.id, 'test_player_id');

      // Then leave the tournament
      final updatedTournament = await dataSource.leaveTournament(
        tournament.id,
        'test_player_id',
      );

      expect(
        updatedTournament.participants.any(
          (p) => p.playerId == 'test_player_id',
        ),
        false,
      );
    });

    test('should return featured tournaments', () async {
      final featuredTournaments = await dataSource.getFeaturedTournaments(
        limit: 3,
      );

      expect(featuredTournaments, isA<List<Tournament>>());
      expect(featuredTournaments.length, lessThanOrEqualTo(3));
      expect(featuredTournaments.every((t) => t.isFeatured), true);
    });

    test('should return nearby tournaments', () async {
      const kathmandu = {'lat': 27.7172, 'lng': 85.3240};

      final nearbyTournaments = await dataSource.getNearbyTournaments(
        latitude: kathmandu['lat']!,
        longitude: kathmandu['lng']!,
        radiusKm: 50.0,
        limit: 10,
      );

      expect(nearbyTournaments, isA<List<Tournament>>());
      expect(nearbyTournaments.length, lessThanOrEqualTo(10));
    });

    test('should search tournaments by term', () async {
      final searchResults = await dataSource.searchTournaments(
        searchTerm: 'championship',
        limit: 5,
      );

      expect(searchResults, isA<List<Tournament>>());
      expect(searchResults.length, lessThanOrEqualTo(5));
    });

    test('should filter tournaments by format', () async {
      const filter = TournamentFilter(formats: [TournamentType.fiveVsFive]);

      final query = TournamentSearchQuery(filter: filter);
      final filteredTournaments = await dataSource.getTournaments(query: query);

      expect(
        filteredTournaments.every(
          (t) => t.format.type == TournamentType.fiveVsFive,
        ),
        true,
      );
    });

    test('should filter tournaments by available slots', () async {
      const filter = TournamentFilter(availableSlotsOnly: true);
      const query = TournamentSearchQuery(filter: filter);

      final filteredTournaments = await dataSource.getTournaments(query: query);

      expect(filteredTournaments.every((t) => t.hasAvailableSlots), true);
    });

    test('should sort tournaments correctly', () async {
      const filter = TournamentFilter(sortBy: TournamentSortBy.lowestFee);
      const query = TournamentSearchQuery(filter: filter);

      final sortedTournaments = await dataSource.getTournaments(query: query);

      if (sortedTournaments.length >= 2) {
        expect(
          sortedTournaments.first.entryFee <= sortedTournaments[1].entryFee,
          true,
        );
      }
    });

    test('should handle pagination correctly', () async {
      const query1 = TournamentSearchQuery(limit: 5, offset: 0);
      const query2 = TournamentSearchQuery(limit: 5, offset: 5);

      final page1 = await dataSource.getTournaments(query: query1);
      final page2 = await dataSource.getTournaments(query: query2);

      expect(page1.length, lessThanOrEqualTo(5));
      expect(page2.length, lessThanOrEqualTo(5));

      if (page1.isNotEmpty && page2.isNotEmpty) {
        expect(page1.first.id, isNot(equals(page2.first.id)));
      }
    });

    group('Favourites Tests', () {
      test('should add tournament to favourites', () async {
        const playerId = 'test_player';
        const tournamentId = 'tournament_1';

        final favourite = await dataSource.addToFavourites(
          playerId: playerId,
          tournamentId: tournamentId,
        );

        expect(favourite.playerId, playerId);
        expect(favourite.tournamentId, tournamentId);
        expect(favourite.isActive, true);
      });

      test('should remove tournament from favourites', () async {
        const playerId = 'test_player';
        const tournamentId = 'tournament_1';

        // Add to favourites first
        await dataSource.addToFavourites(
          playerId: playerId,
          tournamentId: tournamentId,
        );

        // Then remove
        await dataSource.removeFromFavourites(
          playerId: playerId,
          tournamentId: tournamentId,
        );

        final isFavourite = await dataSource.isTournamentFavourite(
          playerId: playerId,
          tournamentId: tournamentId,
        );

        expect(isFavourite, false);
      });

      test('should check if tournament is favourite', () async {
        const playerId = 'test_player';
        const tournamentId = 'tournament_1';

        // Initially should not be favourite
        final isInitiallyFavourite = await dataSource.isTournamentFavourite(
          playerId: playerId,
          tournamentId: tournamentId,
        );
        expect(isInitiallyFavourite, false);

        // Add to favourites
        await dataSource.addToFavourites(
          playerId: playerId,
          tournamentId: tournamentId,
        );

        // Should now be favourite
        final isFavourite = await dataSource.isTournamentFavourite(
          playerId: playerId,
          tournamentId: tournamentId,
        );
        expect(isFavourite, true);
      });

      test('should get player favourite tournaments', () async {
        const playerId = 'test_player';

        final favourites = await dataSource.getFavouriteTournaments(playerId);

        expect(favourites, isA<List<dynamic>>());
      });
    });

    group('Badge System Tests', () {
      test('should return available badges', () async {
        final badges = await dataSource.getAvailableBadges();

        expect(badges, isA<List<dynamic>>());
        expect(badges.length, greaterThan(0));
      });

      test('should return player badges', () async {
        const playerId = 'test_player';

        final playerBadges = await dataSource.getPlayerBadges(playerId);

        expect(playerBadges, isNotNull);
        expect(playerBadges.playerId, playerId);
      });

      test('should return player tournament stats', () async {
        const playerId = 'test_player';

        final stats = await dataSource.getPlayerTournamentStats(playerId);

        expect(stats, isNotNull);
        expect(stats.playerId, playerId);
        expect(stats.tournamentsJoined, isA<int>());
        expect(stats.tournamentsWon, isA<int>());
      });

      test('should check for new badges', () async {
        const playerId = 'test_player';
        final stats = await dataSource.getPlayerTournamentStats(playerId);

        final newBadges = await dataSource.checkForNewBadges(
          playerId: playerId,
          stats: stats,
        );

        expect(newBadges, isA<List<dynamic>>());
      });
    });
  });
}
