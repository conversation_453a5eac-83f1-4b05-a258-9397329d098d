import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:nextsportz_v2/features/tournaments/domain/entities/tournament.dart';
import 'package:nextsportz_v2/features/tournaments/presentation/widgets/tournament_card.dart';

void main() {
  group('TournamentCard Widget Tests', () {
    late Tournament mockTournament;

    setUp(() {
      mockTournament = Tournament(
        id: 'test_tournament_1',
        name: 'Test Championship',
        description: 'A test tournament for football players',
        format: const TournamentFormat(
          type: TournamentType.fiveVsFive,
          playersPerTeam: 5,
          displayName: '5v5',
        ),
        location: 'Test Stadium, Kathmandu',
        organizerId: 'org_1',
        organizerName: 'Test Football Academy',
        startDate: DateTime.now().add(const Duration(days: 7)),
        endDate: DateTime.now().add(const Duration(days: 7, hours: 8)),
        registrationDeadline: DateTime.now().add(const Duration(days: 2)),
        entryFee: 1000.0,
        currency: 'NPR',
        prize: const TournamentPrize(
          totalAmount: 10000.0,
          currency: 'NPR',
          distribution: [
            PrizeDistribution(
              position: 1,
              amount: 5000.0,
              description: 'Winner',
            ),
            PrizeDistribution(
              position: 2,
              amount: 3000.0,
              description: 'Runner-up',
            ),
            PrizeDistribution(
              position: 3,
              amount: 2000.0,
              description: 'Third Place',
            ),
          ],
        ),
        maxParticipants: 16,
        currentParticipants: 8,
        status: TournamentStatus.registrationOpen,
        rules: ['Rule 1', 'Rule 2'],
        tags: ['football', '5v5', 'kathmandu'],
        venueDetails: const TournamentLocation(
          name: 'Test Stadium',
          address: 'Test Address, Kathmandu',
          latitude: 27.7172,
          longitude: 85.3240,
        ),
        createdAt: DateTime.now().subtract(const Duration(days: 10)),
        updatedAt: DateTime.now().subtract(const Duration(days: 1)),
        bannerImage: 'https://example.com/banner.jpg',
        isFeatured: true,
        viewCount: 150,
        favoriteCount: 25,
      );
    });

    Widget createWidgetUnderTest({
      Tournament? tournament,
      VoidCallback? onTap,
      VoidCallback? onFavoriteTap,
      bool isFavorite = false,
      bool showFavoriteButton = true,
    }) {
      return ScreenUtilInit(
        designSize: const Size(375, 812),
        builder:
            (context, child) => MaterialApp(
              home: Scaffold(
                body: TournamentCard(
                  tournament: tournament ?? mockTournament,
                  onTap: onTap,
                  onFavoriteTap: onFavoriteTap,
                  isFavorite: isFavorite,
                  showFavoriteButton: showFavoriteButton,
                ),
              ),
            ),
      );
    }

    testWidgets('should display tournament information correctly', (
      tester,
    ) async {
      await tester.pumpWidget(createWidgetUnderTest());

      // Check if tournament name is displayed
      expect(find.text('Test Championship'), findsOneWidget);

      // Check if description is displayed
      expect(
        find.text('A test tournament for football players'),
        findsOneWidget,
      );

      // Check if location is displayed
      expect(find.text('Test Stadium, Kathmandu'), findsOneWidget);

      // Check if format is displayed
      expect(find.text('5v5'), findsOneWidget);

      // Check if organizer is displayed
      expect(find.text('Test Football Academy'), findsOneWidget);
    });

    testWidgets('should display entry fee correctly', (tester) async {
      await tester.pumpWidget(createWidgetUnderTest());

      expect(find.text('NPR 1000'), findsOneWidget);
    });

    testWidgets('should display "Free" when entry fee is 0', (tester) async {
      final freeTournament = mockTournament.copyWith(entryFee: 0.0);

      await tester.pumpWidget(
        createWidgetUnderTest(tournament: freeTournament),
      );

      expect(find.text('Free'), findsOneWidget);
    });

    testWidgets('should display prize amount correctly', (tester) async {
      await tester.pumpWidget(createWidgetUnderTest());

      expect(find.text('NPR 10000'), findsOneWidget);
    });

    testWidgets('should display available slots correctly', (tester) async {
      await tester.pumpWidget(createWidgetUnderTest());

      expect(find.text('8 slots left'), findsOneWidget);
    });

    testWidgets('should display "Full" when no slots available', (
      tester,
    ) async {
      final fullTournament = mockTournament.copyWith(currentParticipants: 16);

      await tester.pumpWidget(
        createWidgetUnderTest(tournament: fullTournament),
      );

      expect(find.text('Full'), findsOneWidget);
    });

    testWidgets('should show status badge correctly', (tester) async {
      await tester.pumpWidget(createWidgetUnderTest());

      expect(find.text('Registration Open'), findsOneWidget);
    });

    testWidgets('should show favorite button when enabled', (tester) async {
      await tester.pumpWidget(createWidgetUnderTest(showFavoriteButton: true));

      expect(find.byIcon(Icons.favorite_border), findsOneWidget);
    });

    testWidgets('should hide favorite button when disabled', (tester) async {
      await tester.pumpWidget(createWidgetUnderTest(showFavoriteButton: false));

      expect(find.byIcon(Icons.favorite_border), findsNothing);
      expect(find.byIcon(Icons.favorite), findsNothing);
    });

    testWidgets('should show filled heart when tournament is favorite', (
      tester,
    ) async {
      await tester.pumpWidget(createWidgetUnderTest(isFavorite: true));

      expect(find.byIcon(Icons.favorite), findsOneWidget);
    });

    testWidgets('should call onTap when card is tapped', (tester) async {
      bool tapCalled = false;

      await tester.pumpWidget(
        createWidgetUnderTest(onTap: () => tapCalled = true),
      );

      await tester.tap(find.byType(TournamentCard));
      expect(tapCalled, true);
    });

    testWidgets('should call onFavoriteTap when favorite button is tapped', (
      tester,
    ) async {
      bool favoriteTapCalled = false;

      await tester.pumpWidget(
        createWidgetUnderTest(onFavoriteTap: () => favoriteTapCalled = true),
      );

      await tester.tap(find.byIcon(Icons.favorite_border));
      expect(favoriteTapCalled, true);
    });

    testWidgets(
      'should show Join button when registration is open and slots available',
      (tester) async {
        await tester.pumpWidget(createWidgetUnderTest());

        expect(find.text('Join'), findsOneWidget);

        final joinButton = find.widgetWithText(ElevatedButton, 'Join');
        final button = tester.widget<ElevatedButton>(joinButton);
        expect(button.onPressed, isNotNull);
      },
    );

    testWidgets('should show disabled Full button when no slots available', (
      tester,
    ) async {
      final fullTournament = mockTournament.copyWith(currentParticipants: 16);

      await tester.pumpWidget(
        createWidgetUnderTest(tournament: fullTournament),
      );

      expect(find.text('Full'), findsOneWidget);

      final fullButton = find.widgetWithText(ElevatedButton, 'Full');
      final button = tester.widget<ElevatedButton>(fullButton);
      expect(button.onPressed, isNull);
    });

    testWidgets(
      'should show disabled Closed button when registration is closed',
      (tester) async {
        final closedTournament = mockTournament.copyWith(
          status: TournamentStatus.registrationClosed,
        );

        await tester.pumpWidget(
          createWidgetUnderTest(tournament: closedTournament),
        );

        expect(find.text('Closed'), findsOneWidget);

        final closedButton = find.widgetWithText(ElevatedButton, 'Closed');
        final button = tester.widget<ElevatedButton>(closedButton);
        expect(button.onPressed, isNull);
      },
    );

    testWidgets('should format date correctly for different time periods', (
      tester,
    ) async {
      // Test "Today"
      final todayTournament = mockTournament.copyWith(
        startDate: DateTime.now(),
      );

      await tester.pumpWidget(
        createWidgetUnderTest(tournament: todayTournament),
      );
      expect(find.text('Today'), findsOneWidget);
    });

    testWidgets('should display tournament tags correctly', (tester) async {
      await tester.pumpWidget(createWidgetUnderTest());

      // The tags should be displayed somewhere in the card
      // This test verifies the widget doesn't crash with tags
      expect(find.byType(TournamentCard), findsOneWidget);
    });
  });

  group('TournamentCardShimmer Widget Tests', () {
    testWidgets('should display shimmer loading effect', (tester) async {
      await tester.pumpWidget(
        ScreenUtilInit(
          designSize: const Size(375, 812),
          builder:
              (context, child) => const MaterialApp(
                home: Scaffold(body: TournamentCardShimmer()),
              ),
        ),
      );

      expect(find.byType(TournamentCardShimmer), findsOneWidget);

      // Should not crash and should render
      await tester.pumpAndSettle();
    });
  });
}
